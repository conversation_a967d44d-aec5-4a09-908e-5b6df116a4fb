import { Button, CircularProgress, IconButton, Tooltip } from '@mui/material';
import { renderField } from 'pages/component/InputFields';
import PropTypes from 'prop-types';
import { getDateWithTime, hasPermission } from 'utils/helper';
import DeleteIcon from '../../../../assets/images/deletIcon.png';
import EditIcon from '../../../../assets/images/editIcon.png';
import Image from '../../image';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import DownloadDoneOutlinedIcon from '@mui/icons-material/DownloadDoneOutlined';
import SummarizeIcon from '@mui/icons-material/Summarize';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import CloseIcon from '@mui/icons-material/Close';
import BadgeInputComponent from 'pages/component/badge';
import { timeZoneConfig } from 'utils/constant';

const ActionButtons = ({ loading, column, rowData, disabledRow }) => {
  const { multipleButtons, type } = column || {};

  const renderMultipleActionButtons = (buttonConfig) => {
    const {
      buttonOnClick,
      name,
      disabled,
      color,
      showButton,
      module,
      showLoader,
      disabledKey,
      action,
      type,
      tooltip,
      dynamicTooltip,
      isStatusContains,
      text,
      variant
    } = buttonConfig || {};
    const shouldShowButton = showButton
      ? rowData[showButton]
      : isStatusContains
        ? rowData['status'].name.toLowerCase() !== isStatusContains
        : true;
    const isAccessable = module && action ? hasPermission(module, action) : true;

    if (isAccessable && shouldShowButton) {
      if (showLoader && rowData[showLoader]) {
        return <CircularProgress size={'15px'} sx={{ mt: 1 }} />;
      }

      switch (type) {
        case 'button':
          return (
            <Tooltip title={dynamicTooltip && rowData?.[dynamicTooltip] ? rowData[dynamicTooltip] : tooltip || ''}>
              <span style={{ display: 'inline-block' }}>
                {' '}
                <Button
                  size="small"
                  variant={variant || 'contained'}
                  onClick={() => buttonOnClick(type, rowData)}
                  color={color || 'primary'}
                  disabled={disabledKey ? rowData[disabledKey] : disabled || loading}
                  sx={{
                    // mr: '20px',
                    fontSize: '12px',
                    fontWeight: 500,
                    whiteSpace: 'nowrap',
                    '&:hover': {
                      color: '#820014',
                      backgroundColor: 'transparent'
                    }
                  }}
                >
                  {buttonConfig?.buttonTitle || null}
                </Button>
              </span>
            </Tooltip>
          );

        case 'delete':
          return (
            <Tooltip title={tooltip || ''}>
              <IconButton
                onClick={() => buttonOnClick(type, rowData)}
                color={color || 'primary'}
                disabled={disabled}
                className="table-action-icon"
              >
                <Image src={DeleteIcon} alt="Delete" className="table-action-icon" />
              </IconButton>
            </Tooltip>
          );

        case 'deleteOrCancel':
          return (
            <Tooltip title={rowData.disabled ? 'Delete' : 'Cancel' || ''}>
              {rowData.disabled ? (
                <IconButton
                  onClick={() => buttonOnClick(type, rowData)}
                  color={color || 'primary'}
                  disabled={disabled}
                  className="table-action-icon"
                >
                  <Image src={DeleteIcon} alt="Delete" className="table-action-icon" />
                </IconButton>
              ) : (
                <IconButton
                  onClick={() => buttonOnClick(type, rowData)}
                  color={color || 'primary'}
                  disabled={disabled}
                  className="table-action-icon"
                >
                  <CloseIcon />
                </IconButton>
              )}
            </Tooltip>
          );

        case 'meetingListBtns':
          return (
            <div>
              {rowData.meetingDone ? (
                <div className="d-flex g-10">
                  {/* <Button
                    onClick={() => buttonOnClick(type, rowData, 'actionable')}
                    color={color || 'primary'}
                    disabled={disabled}
                    variant="outlined"
                  >
                    Actionable
                  </Button> */}
                  <Button
                    onClick={() => buttonOnClick(type, rowData, 'recordMinutes')}
                    color={color || 'primary'}
                    disabled={disabled}
                    size="small"
                    variant="text"
                    sx={{
                      mr: '20px',
                      fontSize: '12px',
                      fontWeight: 500,
                      whiteSpace: 'nowrap',
                      '&:hover': {
                        color: '#820014',
                        backgroundColor: 'transparent'
                      }
                    }}
                  >
                    Record Minutes
                  </Button>
                </div>
              ) : (
                <Button
                  onClick={() => buttonOnClick(type, rowData, 'reminder')}
                  color={color || 'primary'}
                  disabled={disabled}
                  sx={{
                    mr: '20px',
                    fontSize: '12px',
                    fontWeight: 500,
                    whiteSpace: 'nowrap',
                    '&:hover': {
                      color: '#820014',
                      backgroundColor: 'transparent'
                    }
                  }}
                  variant="text"
                  size="small"
                >
                  Send Reminder
                </Button>
              )}
            </div>
          );

        case 'deptQuotaBtns':
          return (
            <div>
              {
                rowData.disabled && (
                  <Tooltip title="Edit">
                    <IconButton
                      onClick={() => buttonOnClick(type, rowData, 'edit')}
                      color={color || 'primary'}
                      disabled={disabled}
                      className="table-action-icon"
                    >
                      <Image src={EditIcon} alt="Edit" className="table-action-icon" />
                    </IconButton>
                  </Tooltip>
                )
                //  :
                //    <div className='d-flex g-10'>
                //    <Button
                //      onClick={() => buttonOnClick(type, rowData, 'save')}
                //      color={color || 'primary'}
                //      // disabled={disabled}
                //      variant="contained"
                //    >
                //      Save
                //    </Button>
                //    <Tooltip title='Cancel'>
                //    <IconButton
                //      onClick={() => buttonOnClick(type, rowData, 'cancel')}
                //      color={color || 'primary'}
                //      // disabled={disabled}
                //      className="table-action-icon"
                //    >
                //      <CloseIcon />
                //    </IconButton>
                //    </Tooltip>
                //  </div>
              }
            </div>
          );

        case 'generate':
          return (
            <Tooltip title={tooltip || ''}>
              <IconButton
                onClick={() => buttonOnClick(type, rowData)}
                color={color || 'primary'}
                disabled={disabled}
                className="table-action-icon"
                sx={{ fontSize: '20px' }}
              >
                <DownloadDoneOutlinedIcon sx={{ color: color || 'primary' }} color={color || 'primary'} fontSize="120px" />
              </IconButton>
            </Tooltip>
          );

        case 'edit':
          return (
            <Tooltip title={tooltip || ''}>
              <IconButton
                onClick={() => buttonOnClick(type, rowData)}
                color={color || 'primary'}
                disabled={disabled}
                className="table-action-icon"
              >
                <Image src={EditIcon} alt="Edit" className="table-action-icon" />
              </IconButton>
            </Tooltip>
          );

        case 'download':
          return (
            <Tooltip title={tooltip || ''}>
              <IconButton
                onClick={() => buttonOnClick(type, rowData)}
                color={color || 'primary'}
                disabled={disabled}
                className="table-action-icon-download"
              >
                <FileDownloadOutlinedIcon className="table-action-icon-download" color={color || 'primary'} />
              </IconButton>
            </Tooltip>
          );

        case 'editOrSave':
          return (
            <Tooltip title={rowData.disabled ? 'Edit' : 'Save' || ''}>
              {rowData.disabled ? (
                <IconButton
                  onClick={() => buttonOnClick(type, rowData)}
                  color={color || 'primary'}
                  disabled={disabled}
                  className="table-action-icon"
                >
                  <Image src={EditIcon} alt="Edit" className="table-action-icon" />
                </IconButton>
              ) : (
                <Button size="small" variant="contained" onClick={() => buttonOnClick(type, rowData)} color={color || 'primary'}>
                  Save
                </Button>
              )}
            </Tooltip>
          );

        case 'view':
          return (
            <Tooltip title={tooltip || ''}>
              <IconButton
                onClick={() => buttonOnClick(type, rowData)}
                color={color || 'primary'}
                disabled={disabled}
                className="table-action-icon"
              >
                <RemoveRedEyeIcon className="table-action-icon" color={color || 'primary'} />
              </IconButton>
            </Tooltip>
          );
        case 'report':
          return (
            <Tooltip title={tooltip || ''}>
              <IconButton
                onClick={() => buttonOnClick(type, rowData)}
                color={color || 'primary'}
                disabled={disabled}
                className="table-action-icon"
              >
                <SummarizeIcon className="table-action-icon" color={color || 'primary'} />
              </IconButton>
            </Tooltip>
          );

        case 'date':
          return <div>{getDateWithTime(rowData?.[name], timeZoneConfig)}</div>;
        case 'badge':
          return (
            <div className="table-badge-cell">
              <BadgeInputComponent badgeContent={rowData?.[name]} color={rowData?.['color']} />
            </div>
          );

        default:
          return null;
      }
    } else if (showButton && text) {
      return <span style={{ color: 'green' }}>{text}</span>;
    }
    return <div className="default-action"></div>;
  };

  const renderButton = () => {
    switch (type) {
      case 'actions':
        return (
          <div className="table-action-wrapper">
            {multipleButtons?.map((buttonConfig, index) => renderMultipleActionButtons(buttonConfig))}
          </div>
        );
      case 'input':
        return (
          <div className="action-input-field-wrapper">
            {multipleButtons?.map((fieldConfig, index) => {
              const { handleInputChange, name, showButton, module, action, itemName, items, type, ...config } = fieldConfig;

              const shouldShowInput = showButton ? rowData[showButton] : true;
              const isAccessible = module && action ? hasPermission(module, action) : true;

              if (!shouldShowInput || !isAccessible) return null;

              const value = rowData[name];
              const finalItems = itemName ? rowData?.[itemName] || [] : items || [];

              switch (type) {
                case 'defaultText':
                  return <div key={index}>{value}</div>;

                default:
                  return renderField(
                    { ...fieldConfig, items: finalItems },
                    (fieldType, newValue) => handleInputChange(fieldType, name, newValue, rowData),
                    value
                  );
              }
            })}
          </div>
        );
      default:
        return null;
    }
  };

  return <>{renderButton()}</>;
};

ActionButtons.propTypes = {
  onClick: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  column: PropTypes.shape({
    multipleButtons: PropTypes.arrayOf(
      PropTypes.shape({
        icon: PropTypes.node,
        buttonOnClick: PropTypes.func,
        color: PropTypes.string,
        tooltip: PropTypes.string,
        disabled: PropTypes.bool,
        btnValue: PropTypes.string,
        buttonTitle: PropTypes.string
      })
    )
  }),
  disabled: PropTypes.bool,
  value: PropTypes.any,
  rowData: PropTypes.object,
  handleInputChange: PropTypes.func
};

export default ActionButtons;
