import { Box, Typography } from '@mui/material';
import { isEmpty } from 'lodash';
import Image from 'pages/component/image';
import PageTitle from 'pages/component/pageTitle';
import { capitalizeFirstLetter, convertDateToStringFormat } from 'utils/helper';
import { convertUTCToTimezone } from 'utils/timeZoneHelper';

const DocumentViewer = ({ documentData = [], onFileClick }) => {
  const renderFile = (file, fileIndex) => (
    <Box key={fileIndex} onClick={() => onFileClick(file)} className="guide-file-wrapper">
      <Box className="file-wrapper">
        <Image className="file-icon" src={file?.icon} />
      </Box>
      <Typography variant="h6" fontSize={'12px'} fontWeight={500} className="fileName-truncate-text">
        {file?.fileName}
      </Typography>
    </Box>
  );

  const renderDocument = (document, index) => {
    if (!document?.attachments?.length) return null;
    const timeZoneConfig = JSON.parse(localStorage.getItem('selectedArazCityTimeZone'));
    const formattedDate = document?.date ? convertUTCToTimezone(document.date, timeZoneConfig, 'MMMM DD, YYYY') : '';
    const FormattedDate = formattedDate.split('-').join('/');
    const title = `${capitalizeFirstLetter(document?.name)} - ${FormattedDate}`;
    return (
      <Box key={index} mt={3} ml={1} className="asharaguide-container">
        <PageTitle title={title} hideFlag innerContainer className="asharaguide-title" />
        <Box className="asharaguide-innercontainer">
          <Box className="guide-files-wrapper">
            {!isEmpty(document?.attachments) ? (
              document.attachments.map(renderFile)
            ) : (
              <Typography variant="h6" p={3} fontWeight={500} color="secondary" textAlign="center">
                No Document found
              </Typography>
            )}
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <Box className="ashara-guide-files-wrapper" p={'10px'} pt={'5px'}>
      {!isEmpty(documentData) ? (
        documentData.map(renderDocument)
      ) : (
        <Typography variant="h6" p={3} fontWeight={500} color="secondary" textAlign="center">
          No Document found
        </Typography>
      )}
    </Box>
  );
};

export default DocumentViewer;
