const {
  api<PERSON><PERSON><PERSON>,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  REQUIRED,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");

const {
  ArazCity,
  Department,
  Miqaat,
  Jamiat,
  Jamaat,
  Hierarchy,
} = require("../../hierarchy/models");

const mongoose = require("mongoose");
const path = require("path");
const { isEmpty, toObjectId } = require("../../../utils/misc.util");
const {
  redisCacheKeys,
  getCache,
  setCache,
  clearCacheByPattern,
} = require("../../../utils/redis.cache");
const {
  addEventLog,
  EventActions,
  Modules,
} = require("../../../utils/eventLogs.util");

const getAllMiqaats = apiHandler(async (req, res) => {
  let QueryObject = {
    status:
      req.query.status === "all" ? { $in: ["active", "inactive"] } : "active",
  };

  if (isEmpty(req.user.systemRoleID)) {
    const miqaatIDs = req.user.miqaats.map((miqaat) => miqaat.miqaatID);
    QueryObject._id = { $in: miqaatIDs };
  }

  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${
    redisCacheKeys.MIQAAT
  }:${JSON.stringify(QueryObject)}`;

  let data = await getCache(cachekey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Miqaats", data, res, true);
  }

  const miqaats = await Miqaat.find(
    QueryObject,
    "_id name logo status type startDate endDate"
  ).sort({
    _id: -1,
  });

  apiResponse(FETCH, "Miqaats", miqaats, res);

  await setCache(cachekey, miqaats);
});

const addMiqaat = apiHandler(async (req, res) => {
  const {
    name,
    LDName,
    type,
    status,
    description,
    ITSID,
    arazCities,
    startDate,
    endDate,
  } = req.body;
  
  await clearCacheByPattern(`*`);

  let newMiqaat = new Miqaat({
    name,
    LDName,
    type,
    status,
    description,
    ITSID,
    startDate,
    endDate,
    arazCities: toObjectId(arazCities),
    createdBy: req.user._id,
  });

  const Miqaat_Logo_Path = req?.file?.path;

  if (!isEmpty(Miqaat_Logo_Path)) {
    const relativePath = path
      .relative(process.cwd(), Miqaat_Logo_Path)
      .replace(/\\+/g, "/");
    newMiqaat.logo = relativePath;
  }

  await newMiqaat.save();


  await addEventLog(
    EventActions.CREATE,
    Modules.GlobalMasters,
    "Miqaat",
    req.user._id
  );

  return apiResponse(ADD_SUCCESS, "Miqaat", null, res);
});

const getSingleMiqaat = apiHandler(async (req, res) => {
  const { id } = req.params;

  const miqaatData = await Miqaat.aggregate([
    {
      $match: {
        _id: toObjectId(id),
        // status: "active",
      },
    },
    {
      $lookup: {
        from: "arazcities",
        localField: "arazCities",
        foreignField: "_id",
        as: "arazCities",
      },
    },
    {
      $unwind: {
        path: "$arazCities",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: "departments",
        localField: "arazCities.departments.id",
        foreignField: "_id",
        as: "arazCities.departments",
      },
    },
    {
      $lookup: {
        from: "jamaats",
        localField: "arazCities.jamaats",
        foreignField: "_id",
        as: "arazCities.jamaats",
      },
    },
    {
      $lookup: {
        from: "jamiats",
        localField: "arazCities.jamiats",
        foreignField: "_id",
        as: "arazCities.jamiats",
      },
    },
  ]);
  
  if (isEmpty(miqaatData)) {
    return apiError(NOT_FOUND, "Miqaat", null, res);
  }
  return apiResponse(FETCH, "Miqaat", miqaatData[0], res);
});

const editMiqaat = apiHandler(async (req, res) => {
  const {
    id,
    name,
    LDName,
    type,
    status,
    description,
    ITSID,
    arazCities,
    startDate,
    endDate,
  } = req.body;

  await clearCacheByPattern(`*`,id,null);

  let updateData = {
    name,
    LDName,
    type,
    status,
    description,
    ITSID,
    startDate,
    endDate,
    arazCities: toObjectId(arazCities),
    updatedBy: req.user._id,
  };

  const Miqaat_Logo_Path = req?.file?.path;

  if (!isEmpty(Miqaat_Logo_Path)) {
    const relativePath = path
      .relative(process.cwd(), Miqaat_Logo_Path)
      .replace(/\\+/g, "/");
    updateData.logo = relativePath;
  }

  const miqaatData = await Miqaat.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });
  if (isEmpty(miqaatData)) {
    return apiError(NOT_FOUND, "Miqaat", null, res);
  }


  if (status && status === "inactive") {
    // await ArazCity.updateMany(
    //   { miqaatID: miqaatData._id, fasalDate: { $exists: false } },
    //   { $set: { status: false } }
    // )
    await ArazCity.updateMany(
      { miqaatID: miqaatData._id },
      { $set: { status: false } }
    );
  } else if (status && status === "active") {
    // const miqaatArazCities = await ArazCity.find({ miqaatID: miqaatData._id }).select('fasalDate')
    // const fasalArazCityIDs = miqaatArazCities.filter(arazCity => arazCity.fasalDate).map(arazCity => arazCity._id)
    // const notFasalArazCityIDs = miqaatArazCities.filter(arazCity => !arazCity.fasalDate).map(arazCity => arazCity._id)
    // if(fasalArazCityIDs.length) {
    //   await ArazCity.updateMany(
    //     { _id: { $in: fasalArazCityIDs } },
    //     { $set: { status: true } }
    //   )
    // } else {
    //   await ArazCity.updateMany(
    //     { _id: { $in: notFasalArazCityIDs } },
    //     { $set: { status: true } }
    //   )
    // }
  }

  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Miqaat",
    req.user._id
  );

  return apiResponse(UPDATE_SUCCESS, "Miqaat", miqaatData, res);
});

const deleteMiqaat = apiHandler(async (req, res) => {
  const { id } = req.params;
  
  await clearCacheByPattern(`*`,id,null);

  const existsInArazCity = await ArazCity.exists({
    miqaatID: id,
    status: true,
  });

  if (existsInArazCity) {
    return apiError(
      CUSTOM_ERROR,
      "Couldn't Delete Miqaat as being used in ArazCity",
      null,
      res
    );
  }

  const arazCityCounts = await ArazCity.countDocuments({
    miqaatID: id,
  });

  if (arazCityCounts > 0) {
    return apiError(
      CUSTOM_ERROR,
      "Couldn't Delete Miqaat as being used in ArazCity",
      null,
      res
    );
  }

  const miqaatData = await Miqaat.findOneAndDelete({ _id: id });

  if (isEmpty(miqaatData)) {
    return apiError(NOT_FOUND, "Miqaat", null, res);
  }

  await addEventLog(
    EventActions.DELETE,
    Modules.GlobalMasters,
    "Miqaat",
    req.user._id
  );

  apiResponse(DELETE_SUCCESS, "Miqaat", null, res);


  await Hierarchy.deleteMany({ miqaatID: toObjectId(id) });
});

const isActiveMiqaat = apiHandler(async (req, res) => {
  const { id } = req.params;
  const cacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.MIQAAT}:status:${id}`;
  let data = await getCache(cacheKey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Miqaat", data, res);
  }
  const miqaatData = await Miqaat.findOne({ _id: id, status: "active" });

  if (isEmpty(miqaatData)) {
    return apiResponse(NOT_FOUND, "Miqaat", false, res);
  }

  return apiResponse(FETCH, "Miqaat", true, res);
});

module.exports = {
  getAllMiqaats,
  addMiqaat,
  getSingleMiqaat,
  editMiqaat,
  deleteMiqaat,
  isActiveMiqaat,
};
