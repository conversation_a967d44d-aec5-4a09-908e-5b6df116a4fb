import { useDispatch, useSelector } from 'react-redux';
import ZoneMappingForm from './component/zoneMappingForm';
import { useEffect, useState } from 'react';
import { getZoneyByArazCityAction } from 'redux/actions/zoneAction';
import ActionButtons from 'pages/component/table/tableComponent/actionButtons';
import AddEditArazCityZone from '../addEditArazCityZone';
import LargeDataTable from 'pages/component/table';
import CustomModal from 'pages/component/customModal';
import ConfirmationModal from 'pages/component/customModal/confirmationModal';
import { setSingleArazCityZone, setZoneListAction } from 'redux/reducers/zoneReducer';
import { cloneDeep, get } from 'lodash';
import { filterFunction } from 'pages/component/table/tableComponent/tableSearch';
import Loader from 'components/Loader';
import AddEditZoneMapping from './addEditZoneMapping';
import { yupResolver } from '@hookform/resolvers/yup';
import { zoneMappingDefaultValues, zoneMappingSchema } from './component/constant';
import { useForm } from 'react-hook-form';
import { getAllJamaatsAction } from 'redux/actions/arazCityAction';
import ViewZone from '../component/viewZone';
import { getDateWithTime, notification } from 'utils/helper';
import SelectMiqaatAndArazCity from 'pages/component/SelectMiqaatAndArazCity';
import { getAllZoneMappingsAction } from 'redux/actions/zoneCapacity/zoneMapping';
import ViewZoneMapping from './component/ViewZoneMapping';
import { setSingleZoneMappingAction } from 'redux/reducers/zoneCapacity/zoneMappingReducer';
import { exportToExcel } from 'utils/exportToExcel';
import { exporExceltColumns } from '../constant';
import { useAnalytics } from 'utils/userAnalytics';
import { timeZoneConfig } from 'utils/constant';

const ZoneMapping = () => {
  const zonesData = useSelector((state) => state.zone);
  const [zones, setZones] = useState([]);
  const [zone, setZone] = useState({});
  const [allZones, setAllZones] = useState([]);
  const [miqaatAndArazCity, setMiqaatAndArazCity] = useState({});
  const [hideTableBody, setHideTableBody] = useState(true);
  const [loading, setLoading] = useState(false);
  const [addEditModal, setAddEditModal] = useState({
    open: false,
    id: ''
  });
  const [viewModal, setViewModal] = useState({
    open: false,
    id: ''
  });
  const dispatch = useDispatch();
  const { trackUserActivity } = useAnalytics();

  useEffect(() => {
    trackUserActivity({
      page: 'Zone Mapping',
      action: 'page_visit',
      module: 'Muqimeen Mehmaan Mapping'
    });
  }, []);

  const getZoneList = async () => {
    setLoading(true);
    let response = await dispatch(getZoneyByArazCityAction(miqaatAndArazCity?.arazCity));
    setHideTableBody(false);
    setLoading(false);
  };

  useEffect(() => {
    dispatch(getAllJamaatsAction());
  }, [miqaatAndArazCity?.arazCity]);

  useEffect(() => {
    if (miqaatAndArazCity?.arazCity && miqaatAndArazCity?.miqaat) {
      getZoneList();
    } else {
      setViewModal({});
      setAddEditModal({});
      dispatch(setZoneListAction([]));
    }
  }, [miqaatAndArazCity?.arazCity, miqaatAndArazCity?.miqaat]);

  useEffect(() => {
    const zoneList = zonesData?.zoneForSingleArazCity;
    if (zoneList?.length) {
      const data = zoneList.map((item) => ({
        ...item,
        isShowButton: !item.isSynchingZoneMapping,
        isError: item.mehmaanCount < 0
      }));
      setAllZones(data);
      setZones(data);
    }
  }, [zonesData?.zoneForSingleArazCity]);

  const headers = [
    {
      name: 'name',
      type: 'text',
      title: 'Zone Name',
      sortingactive: true,
      width: '200px'
    },
    {
      name: 'capacity',
      type: 'text',
      title: 'Capacity',
      sortingactive: true
    },
    {
      name: 'muqimeenCount',
      type: 'text',
      title: 'Muqimeen Count',
      sortingactive: true
    },
    {
      name: 'mehmaanCount',
      type: 'text',
      errorText: 'isError',
      title: 'Mehmaan Count',
      sortingactive: true
    },
    {
      name: 'isSynchingZoneMapping',
      type: 'synching',
      title: 'Last synced at',
      optionalKey: 'lastSynchedDate',
      sortingactive: false
    },
    {
      name: 'actions',
      type: 'actions',
      title: 'Actions',
      sortingactive: false,
      component: ActionButtons,
      multipleButtons: [
        {
          type: 'view',
          module: 'zoneMapping',
          action: 'view',
          buttonOnClick: (type, rowData) => {
            const { miqaat, arazCity } = miqaatAndArazCity || {};
            if (miqaat && arazCity) {
              setViewModal({ ...viewModal, open: true, id: rowData?._id, zoneName: rowData?.name });
            } else {
              showAlert();
            }
          },
          color: 'primary',
          tooltip: 'View'
        },
        {
          type: 'edit',
          module: 'zoneMapping',
          action: 'edit',
          showButton: 'isShowButton',
          buttonOnClick: (type, rowData) => {
            const { miqaat, arazCity } = miqaatAndArazCity || {};
            if (miqaat && arazCity) {
              setAddEditModal({ ...addEditModal, open: true, id: rowData?._id });
            } else {
              showAlert();
            }
          },
          color: 'primary',
          tooltip: 'Edit'
        }
        // {
        //   type: 'delete',
        //   module: 'zoneMapping',
        //   showButton: 'isNotCMZ',
        //   action: 'delete',
        //   buttonOnClick: (type, rowData) => {
        //     setZone({ id: rowData?._id, arazCityID: rowData?.arazCity?._id });
        //   },
        //   color: 'secondary',
        //   tooltip: 'Delete'
        // }
      ]
    }
  ];

  // const actionButtons = {
  //   title: 'Zone Mapping',
  //   onClick: () => {
  //     let { miqaat, arazCity } = miqaatAndArazCity || {};
  //     if (miqaat && arazCity) {
  //       let url = `${miqaat}/${arazCity}`;
  //       setAddEditModal({ ...addEditModal, open: true, id: '' });
  //     } else {
  //       showAlert();
  //     }
  //   }
  // };

  const handleClose = () => {
    setZone({});
  };

  const handleDelete = async () => {
    const response = await dispatch(deleteArazCityZoneAction(zone));
    if (get(response, 'payload.success', false)) {
      getZoneList();
      setZone({});
    }
  };

  const handleRefresh = () => {
    dispatch(getZoneyByArazCityAction(miqaatAndArazCity?.arazCity));
  };

  const DeleteModalButtons = [
    { label: 'Cancel', onClick: () => handleClose(), color: 'error', disabled: zonesData?.loading },
    { label: 'Confirm', onClick: () => handleDelete(), variant: 'contained', color: 'primary', disabled: zonesData?.loading }
  ];

  const showAlert = () => {
    const { miqaat, arazCity } = miqaatAndArazCity || {};
    let message = '';
    if (!miqaat) {
      message = 'Please select Miqaat';
    } else if (!arazCity) {
      message = 'Please select Araz City';
    }
    dispatch(notification(false, message, true));
  };

  const closeAddEditModal = () => {
    setAddEditModal({ ...addEditModal, open: false, id: '' });
    dispatch(setSingleArazCityZone({}));
  };

  const closeViewModal = () => {
    setViewModal({ ...viewModal, open: false, id: '' });
    dispatch(setSingleZoneMappingAction({}));
  };

  const resetPage = () => {
    setViewModal({});
    setAddEditModal({});
    dispatch(setZoneListAction([]));
  };

  const exportRows = (rows) => {
    const data = cloneDeep([...rows]);
    return data.map((row) => {
      return {
        name: get(row, 'name'),
        capacity: get(row, 'capacity'),
        muqimeenCount: get(row, 'muqimeenCount'),
        mehmaanCount: get(row, 'mehmaanCount'),
        lastSynchedDate: get(row, 'isSynchingZoneMapping') ? 'Syncing' : getDateWithTime(get(row, 'lastSynchedDate'), timeZoneConfig)
      };
    });
  };

  const handleExport = () => {
    const dataToExport = exportRows(allZones);
    exportToExcel(dataToExport, exporExceltColumns, 'zone-mapping');
    trackUserActivity({
      page: 'Zone Mapping',
      action: 'download_report',
      module: 'Muqimeen Mehmaan Mapping'
    });
  };

  const filters = [
    {
      className: 'email-filter-search',
      type: 'search',
      name: 'search',
      // hideField: true,
      title: '',
      label: 'Search',
      placeholder: 'Search...',
      xs: 12,
      sm: 4
    },
    {
      type: 'button',
      name: 'Export',
      disabled: loading,
      label: 'Export Excel',
      onClick: handleExport,
      className: 'kg-filter-search',
      xl: 9,
      sm: 8,
      xs: 12
    }
  ];

  return (
    <>
      {zonesData?.loading && <Loader />}
      <SelectMiqaatAndArazCity
        hideSearchField={true}
        values={miqaatAndArazCity}
        onSelectChange={(data) => {
          resetPage();
          setMiqaatAndArazCity({ ...data });
        }}
      />
      <CustomModal
        cancel={handleClose}
        buttons={DeleteModalButtons}
        borderRadius="20px"
        Component={<ConfirmationModal title="Zone Mapping" />}
        open={zone?.id ? true : false}
      />

      {addEditModal?.open ? (
        <AddEditZoneMapping
          arazCityID={miqaatAndArazCity?.arazCity}
          miqaatId={miqaatAndArazCity?.miqaat}
          zoneMappingId={addEditModal?.id}
          closeAddEditModal={() => closeAddEditModal()}
          miqaatAndArazCity={miqaatAndArazCity}
        />
      ) : viewModal?.open ? (
        <ViewZoneMapping title={viewModal?.zoneName} zoneMappingId={viewModal.id} handleClose={() => closeViewModal()} />
      ) : (
        <LargeDataTable
          title="Zone Mapping"
          showRefreshBtn
          handleRefresh={handleRefresh}
          loading={loading}
          // actionButtons={actionButtons}
          columns={headers}
          filters={filters}
          hideTableBody={hideTableBody}
          rows={allZones}
          // enableSearch
          module={'zoneMapping'}
          allRows={zones}
          enablePagination={true}
          hideRemoveButton={true}
          isMiqaatArazCityFilter={true}
          hideSearchField={hideTableBody}
          miqaatAndArazCity={miqaatAndArazCity}
          onSelectChange={(data) => {
            setMiqaatAndArazCity({ ...data });
          }}
          searchValues={{ search: miqaatAndArazCity?.search }}
          handleSearch={(filteredRows, value) => {
            setMiqaatAndArazCity({ ...miqaatAndArazCity, search: value || '' });
            setAllZones(filteredRows || []);
          }}
        />
      )}
    </>
  );
};
export default ZoneMapping;
