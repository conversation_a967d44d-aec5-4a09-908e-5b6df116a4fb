import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Chip, IconButton, TableBody, TableCell, TableRow, Tooltip, Typography } from '@mui/material';
import { get } from 'lodash';
import AvatarComponent from 'pages/component/avatar';
import BadgeInputComponent from 'pages/component/badge';
import CustomInputField from 'pages/component/InputFields/customInputField';
import CustomSelect from 'pages/component/InputFields/customSelect';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { capitalizeFirstLetter, convertDateToStringFormat, formatTimeOnly, getDateWithTime, getImageWithBaseUrl } from 'utils/helper';
import ActionCell from './actionCell';
import InboxAttachments from 'pages/component/inbox/inboxAttachments';
import DoubleTextBox from './showDoubleTextBox';
import { convertUTCToTimezone } from 'utils/timeZoneHelper';

const CustomTableBody = ({
  rows,
  columns,
  isLoading,
  disabledRow,
  handleFileOpen,
  isDeleteButtonVisible,
  rowTooltip,
  removeTableRowpadding,
  tableBodyClassName,
  cellClassName
}) => {
  const renderTableCell = (column, row, rowIndex, columnIndex) => {
    const {
      name,
      isCapitalize,
      type,
      keyName,
      dateFormat,
      includeTime,
      showToday,
      className,
      textColorOnly,
      badgeClassName,
      isButtonVisible,
      Component,
      optionalKey
    } = column || {};
    const cellValue = get(row, name, '') === 0 ? '0' : get(row, name, '');
    const timeZoneConfig = JSON.parse(localStorage.getItem('selectedArazCityTimeZone'));

    switch (type) {
      case 'razaStatus':
        return (
          <Chip
            label={cellValue ? 'Has Raza' : 'No Raza'}
            color={cellValue ? 'success' : 'error'}
            variant="outlined"
            sx={{
              fontWeight: 'bold',
              fontSize: { xs: '0.7rem', sm: '0.875rem' },
              height: { xs: 22, sm: 28 }
            }}
          />
        );
      case 'image':
        return <AvatarComponent alt={cellValue} src={getImageWithBaseUrl(cellValue)} width={'35px'} height={'35px'} />;
      case 'file':
        return <InboxAttachments attachments={cellValue} handleOpen={handleFileOpen} />;
      case 'phone':
        return cellValue ? (
          <a href={`tel:${cellValue}`} style={{ color: 'red', textDecoration: 'none' }}>
            {cellValue}
          </a>
        ) : (
          '-'
        );

      case 'date':
        return cellValue
          ? includeTime
            ? convertUTCToTimezone(cellValue, timeZoneConfig, dateFormat, includeTime)
            : convertUTCToTimezone(cellValue, timeZoneConfig, dateFormat)
          : '-';

      case 'enLD':
        return cellValue ? <Typography dangerouslySetInnerHTML={{ __html: cellValue }} mt={1} ml={1} mr={1} /> : '-';
      case 'time':
        return cellValue ? formatTimeOnly(cellValue, timeZoneConfig) : '-';

      case 'component':
        return <Component rowData={row} />;

      case 'info':
        return (
          get(row, name, '') && (
            <Tooltip title={cellValue} aria-label="">
              <IconButton aria-label="" onClick={() => onClick('info')}>
                <InfoOutlinedIcon sx={{ fontSize: '16px' }} />
              </IconButton>
            </Tooltip>
          )
        );

      case 'price':
        return cellValue ? formatPrice(cellValue) : '-';

      case 'email':
        return <span className="email-table-cell">{cellValue}</span>;

      case 'link':
        return (
          <Link to={cellValue || '#'} className="link" target="_blank" rel="noopener noreferrer">
            Link
          </Link>
        );

      case 'synching':
        return cellValue ? <span className="badge-cell">Syncing</span> : <span>{getDateWithTime(row[optionalKey], timeZoneConfig)}</span>;
      case 'doubleTextBox':
        return cellValue ? <DoubleTextBox values={cellValue} /> : '-';

      case 'badge':
        if (name === 'status') {
          let statusLabel, statusColor;
          if (typeof cellValue === 'boolean') {
            statusLabel = cellValue ? 'Active' : 'Inactive';
            statusColor = cellValue ? 'rgb(88,168,48)' : 'rgba(168, 48, 48, 1)';
          } else if (typeof cellValue === 'string' && (cellValue.toLowerCase() === 'active' || cellValue.toLowerCase() === 'inactive')) {
            statusLabel = cellValue.charAt(0).toUpperCase() + cellValue.slice(1).toLowerCase();
            statusColor = cellValue.toLowerCase() === 'active' ? 'rgb(88,168,48)' : 'rgba(168, 48, 48, 1)';
          } else {
            statusLabel = cellValue;
            statusColor = undefined; // Let BadgeInputComponent decide
          }
          return (
            <div className="table-badge-cell">
              <BadgeInputComponent
                badgeType={column?.badgeType}
                textColorOnly={textColorOnly}
                badgeContent={statusLabel}
                color={statusColor}
                onClick={() => {}}
                className={badgeClassName}
              />
            </div>
          );
        }
        return (
          <div className="table-badge-cell">
            <BadgeInputComponent
              badgeType={column?.badgeType}
              textColorOnly={textColorOnly}
              badgeContent={typeof cellValue === 'object' ? cellValue?.name : cellValue}
              color={cellValue?.color}
              onClick={() => {}}
              className={badgeClassName}
            />
          </div>
        );

      case 'array':
        const arrayValues = cellValue;
        if (Array.isArray(arrayValues)) {
          return (
            <div className="table-array-cell">
              {arrayValues.map((value, idx) => (
                <div key={idx} className={`table-array-value ${cellClassName ? cellClassName : ''}`}>
                  {renderTableCell({ ...column, type: column?.objectType }, { ...row, [name]: value }, rowIndex, columnIndex)}
                </div>
              ))}
            </div>
          );
        }
        return '-';

      case 'select':
        return (
          <CustomSelect
            key={row[column.id]}
            id={row[column.id]}
            name={column.name}
            value={row[column.name]}
            placeholder={column.placeholder}
            handleChange={(e) => column.handleChange(e, row)}
            items={column.itemsFromRow ? row[column.rowKey] : column.items}
            multiple={column.multiple}
            onChange={(e) => column.handleInputChange(e, row)}
            className={column.className}
            disabled={row.disabled}
          />
        );

      case 'inputField':
        return (
          <CustomInputField
            key={row[column.id]}
            id={row[column.id]}
            name={column.name}
            isNumeric={column.isNumeric}
            hideCrossIcon={true}
            onKeyDown={column.onKeyDown}
            placeholder={column.placeholder}
            value={row[column.name]}
            onChange={(e) => column.handleInputChange(e, row)}
            type={column.inputType}
            className={column.className}
            disabled={row.disabled}
          />
        );

      case 'actions':
      case 'input':
        return isButtonVisible ? (
          row[isButtonVisible] ? (
            <ActionCell
              column={column}
              row={row}
              isLoading={isLoading}
              isDeleteButtonVisible={isDeleteButtonVisible}
              disabledRow={row.disabled}
            />
          ) : null
        ) : (
          <ActionCell
            column={column}
            row={row}
            isLoading={isLoading}
            isDeleteButtonVisible={isDeleteButtonVisible}
            disabledRow={row.disabled}
          />
        );
      default:
        return cellValue ? capitalizeFirstLetter(cellValue) : '-';
    }
  };

  const getRowClass = (row, rowIndex) => {
    if (row?.error) return 'table-row-error';
    if (row?.isAlreadyExists) return 'table-row-exists';
    return rowIndex % 2 === 0 ? 'table-row-even' : 'table-row-odd';
  };

  const getRowStyle = (row) =>
    row[disabledRow] ? { pointerEvents: 'none', backgroundColor: 'rgb(229 229 229)' } : { pointerEvents: 'auto' };

  const getCellStyle = (row) => (row[disabledRow] ? { pointerEvents: 'none' } : { pointerEvents: 'auto' });

  const hasActionButton = () => {
    return columns?.some((col) => col?.name === 'actions');
  };

  return (
    <TableBody>
      {rows?.map((row, rowIndex) => {
        const rowClass = getRowClass(row, rowIndex);
        const rowStyle = getRowStyle(row);

        return (
          <TableRow key={rowIndex} className={rowClass} style={rowStyle}>
            {columns?.map((column, columnIndex) => {
              const cellStyle = getCellStyle(row);
              return (
                <TableCell
                  key={columnIndex}
                  className={`${column.name === 'name' ? tableBodyClassName : ''} default-table-cell ${column.name === 'userName' ? cellClassName : ''}`}
                  style={{
                    ...cellStyle,
                    padding: !removeTableRowpadding && !hasActionButton() && '14px',
                    paddingLeft: '10px',
                    color: row[column?.errorText] && 'red',
                    maxWidth: column?.maxWidth,
                    minWidth: column?.minWidth,
                    width: column?.width,
                    whiteSpace: column.wordWrap ? 'normal' : '',
                    wordBreak: column.wordWrap ? 'break-word' : '',
                    overflowWrap: column.wordWrap ? 'break-word' : ''
                  }}
                >
                  {renderTableCell(column, row, rowIndex, columnIndex)}
                </TableCell>
              );
            })}
          </TableRow>
        );
      })}
    </TableBody>
  );
};

CustomTableBody.propTypes = {
  rows: PropTypes.arrayOf(PropTypes.object).isRequired,
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      keyName: PropTypes.string,
      dateFormat: PropTypes.string,
      includeTime: PropTypes.bool,
      showToday: PropTypes.bool,
      className: PropTypes.string,
      textColorOnly: PropTypes.bool,
      badgeClassName: PropTypes.string,
      isButtonVisible: PropTypes.string,
      arrayType: PropTypes.string,
      actionButtons: PropTypes.func
    })
  ).isRequired,
  isLoading: PropTypes.bool.isRequired,
  rowTooltip: PropTypes.string,
  disabledRow: PropTypes.string,
  isDeleteButtonVisible: PropTypes.bool.isRequired,
  renderArrayCell: PropTypes.func.isRequired,
  getDateWithTime: PropTypes.func.isRequired,
  formatDate: PropTypes.func.isRequired,
  formatPrice: PropTypes.func.isRequired
};

export default CustomTableBody;
