import FilterListIcon from '@mui/icons-material/FilterList';
import { Button } from '@mui/material';
import { Stack } from '@mui/system';
import Loader from 'components/Loader';
import { get, isEmpty } from 'lodash';
import moment from 'moment';
import { exportToExcel } from 'pages/component/kgUsers/constant';
import LargeDataTable from 'pages/component/table';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import { getAllJamaatsAction, getAllJamiatsAction } from 'redux/actions/arazCityAction';
import { getActiveDepartmentByArazCityAction } from 'redux/actions/departmentsAction';
import { getAllKGExportRequestAction, getAllKGRequestAction } from 'redux/actions/HR/khidmatRequestAction';
import { setAllJamatAction } from 'redux/reducers/arazCityReducer';
import { setAllKGReqAction } from 'redux/reducers/khidmatRequestReducer';
import { getUserDetail } from 'utils/auth';
import { SUPERADMIN, timeZoneConfig } from 'utils/constant';
import { getImageWithBaseUrl, getOptions, isQasreAliFemale, notification, placeholderImg, showMiqaatValidationAlert } from 'utils/helper';
import CustomModal from '../../customModal';
import ActionButtons from '../../table/tableComponent/actionButtons';
import AssignKGRequest from './assignKGRequest';
import { KGRequestTabFilters } from './constant';
import KGPoolFilter from './filters';
import { useAnalytics } from 'utils/userAnalytics';
import { convertUTCToTimezone } from 'utils/timeZoneHelper';

const initialFilterValues = {
  gender: ['male', 'female']
};

const KGRequestReport = () => {
  const dapartmentData = useSelector((state) => state.departments);
  const kgRequestData = useSelector((state) => state.khidmatRequest);
  const kgData = useSelector((state) => state.kgData);
  const zoneData = useSelector((state) => state.zone);
  const arazCity = useSelector((state) => state.arazCity);
  const [kgReport, setKGReport] = useState([]);
  const [allKGReport, setAllKGReport] = useState([]);
  const [singleKGRequest, setSingleKGRequest] = useState({});
  const [miqaatAndArazCity, setMiqaatAndArazCity] = useState({});
  const [hideTableBody, setHideTableBody] = useState(true);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [totalCount, setTotalCount] = useState(1);
  const [limit, setLimit] = useState(50);
  const navigate = useNavigate();
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [searchValues, setSearchValues] = useState(initialFilterValues);
  const [appliedFilters, setAppliedFilters] = useState(initialFilterValues);
  const dispatch = useDispatch();
  const { trackUserActivity } = useAnalytics();

  useEffect(() => {
    dispatch(getAllJamiatsAction());
  }, []);

  useEffect(() => {
    if (miqaatAndArazCity?.arazCity) {
      trackUserActivity({
        action: 'page_visit',
        page: 'KG Pool',
        module: 'HR & Hierarchy'
      });
      let selectedJamiaat = arazCity?.arazCityForSingleMiqaat?.find((arazCity) => arazCity?._id === miqaatAndArazCity?.arazCity);
      if (!isEmpty(selectedJamiaat?.jamiats)) {
        dispatch(getAllJamaatsAction({ ids: selectedJamiaat?.jamiats || [] }));
      } else {
        dispatch(setAllJamatAction([]));
      }
    } else {
      dispatch(setAllJamatAction([]));
    }
  }, [miqaatAndArazCity?.arazCity, arazCity?.arazCityForSingleMiqaat]);

  useEffect(() => {
    if (miqaatAndArazCity?.arazCity) {
      dispatch(getActiveDepartmentByArazCityAction(miqaatAndArazCity?.arazCity));
    }
  }, [miqaatAndArazCity?.arazCity]);

  const formatKGRequestData = (data) => {
    const sortedData = [...data];

    return (
      sortedData.map((item) => {
        const { interestOne, interestTwo, interestThree } = item?.interestData || {};

        const interestOneDepartment = get(interestOne, 'departmentID.name', '') || '';
        const interestTwoDepartment = get(interestTwo, 'departmentID.name', '') || '';
        const interestThreeDepartment = get(interestThree, 'departmentID.name', '') || '';
        return {
          ...item,
          logo: item?.logo
            ? isQasreAliFemale(item?.ITSID, item?.gender)
              ? placeholderImg(item?.name)
              : getImageWithBaseUrl(item?.logo)
            : '',
          status: item?.status || 'not-assigned',
          isNotAssigned: item?.status === 'not-assigned',
          jamiatID: item?.jamiatID?._id,
          jamiatName: item?.jamiatID?.name,
          interestOne: interestOne || {},
          interestTwo: interestTwo || {},
          interestThree: interestThree || {},
          jamaatID: item?.jamaatID?._id,
          mobile: item?.phone,
          date: item?.createdAt,
          zoneName: item?.zone?.name,
          arazCityZoneID: item?.zoneID?._id || '',
          miqaatZone: item?.miqaatZone || '',
          jamaatName: item?.jamaatID?.name,
          updatedAtRaw: item?.updatedAt,
          qualification: item?.qualification || '',
          gender: get(item, 'gender', '') === 'M' ? 'Male' : get(item, 'gender', '') === 'F' ? 'Female' : '',
          updatedAt:
            item?.updatedAt && timeZoneConfig ? convertUTCToTimezone(item.updatedAt, timeZoneConfig, 'DD-MM-YYYY HH:mm', true) : '',
          priorityOne: interestOneDepartment,
          priorityTwo: interestTwoDepartment,
          priorityThree: interestThreeDepartment,
          departments: [interestOneDepartment, interestTwoDepartment, interestThreeDepartment],
          appDetail: get(item, 'appDetails.deviceType')
            ? `${get(item, 'appDetails.deviceType', '')} - ${get(item, 'appDetails.version', '')}`
            : 'Not Installed',
          departmentData: [
            {
              color: '#39ac12',
              name: interestOneDepartment,
              id: get(interestOne, 'departmentID._id', '')
            },
            {
              color: '#E5CE89',
              name: interestTwoDepartment,
              id: get(interestTwo, 'departmentID._id', '')
            },
            {
              color: '#f26262',
              name: interestThreeDepartment,
              id: get(interestThree, 'departmentID._id', '')
            }
          ].filter((d) => d.name),
          razaStatus: get(item, 'razaStatus.RazaStatus') === 'Has Raza',
          miqaatZone: get(item, 'razaStatus.MiqaatZone') || '-'
        };
      }) || []
    );
  };

  const filteredData = useMemo(() => {
    setTotalCount(get(kgRequestData, 'allKGRequest.pagination.totalCount', 0));
    const sortedData = [...get(kgRequestData, 'allKGRequest.users', [])];

    let updatedData = formatKGRequestData(sortedData);

    return updatedData || [];
  }, [kgRequestData?.allKGRequest]);

  useEffect(() => {
    setAllKGReport(filteredData || []);
  }, [filteredData]);

  useEffect(() => {
    if (!isEmpty(filteredData)) {
      setKGReport(filteredData);
    } else {
      setKGReport([]);
    }
  }, [filteredData]);

  const headers = [
    {
      name: 'logo',
      type: 'image',
      title: '',
      sortingactive: true,
      width: '100px'
    },
    {
      name: 'ITSID',
      type: 'text',
      title: 'ITS ID',
      sortingactive: true,
      width: '100px'
    },
    {
      name: 'name',
      type: 'text',
      title: 'Name',
      sortingactive: true,
      width: '300px'
    },
    {
      name: 'razaStatus',
      type: 'razaStatus',
      title: 'Raza Status',
      minWidth: '100px',
      sortingactive: true
    },
    {
      name: 'miqaatZone',
      type: 'text',
      title: 'Miqaat Zone',
      sortingactive: true,
      minWidth: '200px'
    },
    {
      name: 'zoneName',
      type: 'text',
      title: 'Zone',
      sortingactive: true,
      minWidth: '200px'
    },
    {
      name: 'gender',
      type: 'text',
      title: 'Gender',
      sortingactive: true,
      minWidth: '100px'
    },
    {
      name: 'mobile',
      type: 'phone',
      title: 'Mobile Number',
      width: '120px'
    },
    {
      name: 'whatsapp',
      type: 'phone',
      title: 'WhatsApp Number',
      width: '120px'
    },
    {
      name: 'jamiatName',
      type: 'text',
      title: 'Jamiat',
      sortingactive: true,
      width: '150px'
    },
    {
      name: 'jamaatName',
      type: 'text',
      title: 'Jamaat',
      sortingactive: true,
      width: '200px'
    },
    {
      name: 'qualification',
      type: 'text',
      title: 'Qualification',
      sortingactive: true,
      width: '120px'
    },
    {
      name: 'occupation',
      type: 'text',
      title: 'Occupation',
      sortingactive: true,
      width: '130px'
    },
    {
      name: 'priorityOne',
      type: 'text',
      title: 'Priority One',
      width: '200px'
    },
    {
      name: 'priorityTwo',
      type: 'text',
      title: 'Priority Two',
      width: '200px'
    },
    {
      name: 'priorityThree',
      type: 'text',
      title: 'Priority Three',
      width: '200px'
    },
    {
      type: 'badge',
      name: 'status',
      title: 'Status',
      width: '300px'
    },
    {
      name: 'updatedAt',
      type: 'text',
      title: 'Request Received Date',
      sortingactive: true,
      width: '200px'
    },
    {
      name: 'appDetail',
      type: 'text',
      title: 'Mobile App',
      minWidth: '200px'
    },
    {
      name: 'actions',
      type: 'actions',
      title: 'Actions',

      sortingactive: false,
      component: ActionButtons,
      multipleButtons: [
        {
          type: 'button',
          buttonTitle: 'Assign',
          module: 'kgUser',
          action: 'add',
          buttonOnClick: (type, rowData) => {
            setSingleKGRequest({ ...rowData });
          },
          showButton: 'isNotAssigned',
          tooltip: 'Assign'
        }
      ]
    }
  ];

  const getSortKey = (key) => {
    switch (key) {
      case 'jamaatName':
        return 'jamaat';
      case 'jamiatName':
        return 'jamiat';
      case 'zoneName':
        return 'zone';
      default:
        return key || '';
    }
  };

  const createFilter = (filterValues = {}) => {
    return {
      search: filterValues?.search || '',
      sortBy: getSortKey(filterValues?.sortBy) || '',
      sortOrder: filterValues?.sortOrder || '',
      priority:
        filterValues?.priority === 'priorityOne'
          ? '1'
          : filterValues?.priority === 'priorityTwo'
            ? '2'
            : filterValues?.priority === 'priorityThree'
              ? '3'
              : '',
      department: filterValues?.departments || '',
      arazCityZoneID: filterValues?.zones || [],
      jamaat: filterValues?.jamaatName || '',
      status: filterValues?.status !== 'all' ? filterValues?.status : '',
      qualification: filterValues?.qualification || '',
      occupation: filterValues?.occupation || '',
      gender: filterValues?.gender || [],
      deviceType: filterValues?.deviceType || [],
      razaStatus: filterValues?.razaStatus || [],
      miqaatZone: filterValues?.miqaatZone || ''
    };
  };

  const getAllReport = async (filterValues) => {
    setLoading(true);
    let filter = createFilter(filterValues);

    let updatedPage = page ? page + 1 : '1';
    let payload = {
      miqaatID: miqaatAndArazCity?.miqaat,
      arazCityID: miqaatAndArazCity?.arazCity,
      limit: limit ? limit?.toString() : '50',
      page: updatedPage?.toString() || '1',
      ...filter
    };
    let response = await dispatch(getAllKGRequestAction(payload));
    setHideTableBody(false);
    setIsFilterOpen(false);
    setLoading(false);
  };

  useEffect(() => {
    if (miqaatAndArazCity?.miqaat && miqaatAndArazCity?.arazCity) {
      getAllReport(searchValues);
    } else {
      dispatch(setAllKGReqAction([]));
      setHideTableBody(true);
    }
  }, [
    miqaatAndArazCity?.miqaat,
    miqaatAndArazCity?.arazCity,
    limit,
    page,
    searchValues?.search,
    searchValues?.sortBy,
    searchValues?.sortOrder
  ]);

  const handleClose = () => {
    setSingleKGRequest({});
  };

  const getDepartmentsBasedOnPriority = (priority) => {
    return getOptions(dapartmentData?.departmentsforArazCity || [], 'name', 'id');
  };

  const openFilter = () => setIsFilterOpen(!isFilterOpen);
  const closeFilter = () => {
    setSearchValues({ ...appliedFilters, search: searchValues?.search });
    setIsFilterOpen(false);
  };

  const KGRequestFilters = [
    { type: 'search', xl: 2, sm: 2, name: 'search', label: 'Search', placeholder: 'Search...' },
    {
      type: 'component',
      component: ({ allRows, headers }) => {
        return (
          <Stack direction="row" width="100%" alignItems="center" justifyContent="flex-end">
            <Button variant="contained" onClick={openFilter} startIcon={<FilterListIcon />}>
              Filter
            </Button>
          </Stack>
        );
      },
      className: 'kg-filter-search',
      md: 10,
      xl: 10,
      sm: 10
    }
  ];

  const filterFields = [
    {
      type: 'autoselect',
      name: 'jamaatName',
      label: 'Jamaat',
      placeholder: 'Select Jamaat',
      xl: 6,
      sm: 6,
      items: getOptions(arazCity?.allJamaats, 'name')
    },
    {
      type: 'autoselect',
      name: 'priority',
      ignoreFilter: true,
      hideSorting: true,
      label: 'Priority',
      dependencyField: ['priorityOne', 'priorityTwo', 'priorityThree'],
      placeholder: 'Select Priority',
      xl: 6,
      sm: 6,
      items: [
        { label: 'Priority One', value: 'priorityOne' },
        { label: 'Priority Two', value: 'priorityTwo' },
        { label: 'Priority Three', value: 'priorityThree' }
      ]
    },
    {
      type: 'autoselect',
      name: 'departments',
      label: 'Department',
      xl: 6,
      sm: 6,
      placeholder: 'Select Department',
      items: getDepartmentsBasedOnPriority(searchValues?.['priority'] || '')
    },
    {
      type: 'autoselect',
      name: 'zones',
      label: 'Zone',
      xl: 6,
      sm: 6,
      multiple: true,
      placeholder: 'Select Zone',
      items: getOptions(zoneData?.zoneForSingleArazCity, 'name')
    },
    {
      type: 'text',
      name: 'miqaatZone',
      label: 'Miqaat Zone',
      xl: 6,
      sm: 6,
      multiple: true,
      placeholder: 'Search Miqaat Zone'
    },
    {
      name: 'status',
      type: 'autoselect',
      label: 'Status',
      placeholder: 'Select Status',
      items: [
        { label: 'Assigned', value: 'assigned' },
        { label: 'Not Assigned', value: 'not-assigned' }
      ]
    },
    {
      name: 'deviceType',
      type: 'autoselect',
      label: 'App Installed Status',
      placeholder: 'Select Status',
      multiple: true,
      items: [
        { label: 'Android', value: 'ANDROID' },
        { label: 'iOS', value: 'IOS' },
        { label: 'Not Installed', value: 'not-installed' }
      ]
    },
    {
      name: 'razaStatus',
      type: 'autoselect',
      label: 'Raza Status',
      placeholder: 'Select Status',
      multiple: true,
      items: [
        { label: 'Has Raza', value: 'Has Raza' },
        { label: 'No Raza', value: 'No Raza' }
      ]
    },
    {
      name: 'gender',
      type: 'checkbox-group',
      label: 'Gender',
      defaultValue: ['male', 'female'],
      row: true,
      xl: 6,
      sm: 6,
      items: [
        { label: 'Male', value: 'male' },
        { label: 'Female', value: 'female' }
      ]
    }
  ];

  const actionButtons = [
    {
      title: 'Export Excel',
      hideAddIcon: true,
      hideButton: getUserDetail()?.role !== SUPERADMIN,
      onClick: async () => {
        const { miqaat, arazCity } = miqaatAndArazCity;
        if (miqaat && arazCity) {
          let filter = createFilter(searchValues);
          let payload = { miqaatID: miqaatAndArazCity?.miqaat, arazCityID: miqaatAndArazCity?.arazCity };
          if (!isEmpty(filter)) payload = { ...payload, ...filter };
          let response = await dispatch(getAllKGExportRequestAction(payload));
          let responseData = get(response, 'payload.data.users.[0].data', []);
          if (response?.payload?.success) {
            if (!isEmpty(responseData)) {
              exportToExcel(headers, formatKGRequestData(response?.payload?.data?.users?.[0]?.data), 'KGRequests');
              trackUserActivity({
                action: 'downlaod_report',
                page: 'KG Pool',
                module: 'HR & Hierarchy'
              });
            } else {
              dispatch(notification(false, 'No data found', true));
            }
          } else {
            dispatch(notification(false, response?.payload?.message || 'No data found', true));
          }
        } else {
          showMiqaatValidationAlert(miqaatAndArazCity, dispatch);
        }
      }
    }
  ];

  const filterKGRequests = (data = [], searchValues = {}, filterConfig = []) => {
    const ignoredFields = new Set(filterConfig.filter((f) => f.ignoreFilter).map((f) => f.name));

    return data.filter((item) =>
      Object.entries(searchValues).every(([key, value]) => {
        if (!value || ignoredFields.has(key)) return true;

        const searchVal = String(value).toLowerCase();

        switch (key) {
          case 'departments': {
            const { priority } = searchValues;

            const departmentMatches = (interest) => interest?.departmentID?.name?.toLowerCase().includes(searchVal);

            if (priority === 'priorityOne') return departmentMatches(item.interestOne);
            if (priority === 'priorityTwo') return departmentMatches(item.interestTwo);
            if (priority === 'priorityThree') return departmentMatches(item.interestThree);

            return departmentMatches(item.interestOne) || departmentMatches(item.interestTwo) || departmentMatches(item.interestThree);
          }

          case 'jamiatName':
            return item?.jamaatName?.toLowerCase() === searchVal;

          case 'search':
            return Object.values(item || {})?.some(
              (val) => ['string', 'number']?.includes(typeof val) && String(val)?.toLowerCase()?.includes(searchVal)
            );

          case 'status':
            return searchVal === 'all' || item?.status?.toLowerCase() === searchVal;

          default:
            return true;
        }
      })
    );
  };

  const onPageChange = (newPage) => {
    setPage(newPage);
  };

  const onPageLimitChange = (limit) => {
    setPage(0);
    setLimit(limit);
  };

  const handleSort = (sortingData) => {
    setSearchValues({ ...searchValues, sortBy: sortingData?.key, sortOrder: sortingData?.value });
  };

  const handleInputFilter = (type, value, name) => {
    setSearchValues({ ...searchValues, [name]: value });
  };

  const handleApplyFilter = () => {
    setPage(0);
    setAppliedFilters({ ...searchValues, search: searchValues?.search }); // save the applied state
    if (miqaatAndArazCity?.miqaat && miqaatAndArazCity?.arazCity) {
      getAllReport(searchValues);
    } else {
      dispatch(setAllKGReqAction([]));
      setHideTableBody(true);
    }
  };

  const clearFilter = () => {
    setSearchValues({ ...initialFilterValues, search: searchValues?.search });
    getAllReport({ ...initialFilterValues, search: searchValues?.search });
  };

  return (
    <>
      {(kgRequestData?.loading || kgData?.loading) && <Loader />}
      {miqaatAndArazCity?.arazCity && miqaatAndArazCity?.miqaat && !hideTableBody && (
        <>
          {' '}
          <CustomModal
            cancel={handleClose}
            borderRadius="20px"
            Component={
              <AssignKGRequest
                handleClose={handleClose}
                singleKGRequest={singleKGRequest}
                miqaatID={miqaatAndArazCity?.miqaat}
                arazCityID={miqaatAndArazCity?.arazCity}
                getAllReport={() => getAllReport(searchValues)}
              />
            }
            open={!isEmpty(singleKGRequest) ? true : false}
          />{' '}
        </>
      )}
      {isFilterOpen && !hideTableBody && (
        <>
          {' '}
          <CustomModal
            cancel={closeFilter}
            borderRadius="20px"
            Component={
              <KGPoolFilter
                handleClose={closeFilter}
                clearFilter={clearFilter}
                filters={filterFields}
                handleFilter={handleInputFilter}
                values={searchValues}
                handleApplyFilter={handleApplyFilter}
              />
            }
            open={isFilterOpen}
          />{' '}
        </>
      )}

      <LargeDataTable
        loading={loading}
        title="KG Pool"
        filters={KGRequestFilters}
        searchValues={searchValues}
        actionButtons={actionButtons}
        isMiqaatArazCityFilter={true}
        showApplyButton={true}
        hideMainCard={!miqaatAndArazCity?.miqaat || !miqaatAndArazCity?.arazCity || hideTableBody}
        tableContainerClassName="kg-list-table sticky-table-container"
        miqaatAndArazCity={miqaatAndArazCity}
        onSelectChange={(data) => {
          setMiqaatAndArazCity({ ...data });
          if (!data?.miqaat || !data?.arazCity) {
            setSearchValues({ status: 'all' });
          }
        }}
        applyFilterOnKeyDown={true}
        tabFilter={KGRequestTabFilters}
        isTabFilter={true}
        hideTableBody={hideTableBody}
        columns={headers}
        module="KGRequestReport"
        rows={kgReport || []}
        allRows={allKGReport || []}
        enablePagination={true}
        // hideFilterFunction={true}
        isBackendPagination={true}
        removeStickyHeader
        totalCount={totalCount}
        pageNo={page}
        handleSort={(sortingData) => handleSort(sortingData)}
        onPageChange={(page) => onPageChange(page)}
        onPageLimitChange={(page) => onPageLimitChange(page)}
        handleSearch={(filteredRows, searchValue, name) => {
          setPage(0);
          setSearchValues({ ...searchValues, ...searchValue });
          setMiqaatAndArazCity({ ...miqaatAndArazCity, ...searchValue });
        }}
      />
    </>
  );
};

export default KGRequestReport;
