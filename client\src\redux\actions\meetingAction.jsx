import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest, putRequest } from 'utils/axios';

import { CommunicationMeetingApiUrl } from 'utils/constant';

const getMeetingUrl = (endponints) => {
  return `${CommunicationMeetingApiUrl}${endponints}`;
};

const getMeetingsList = (data, dispatch) => {
  return postRequest('communication', getMeetingUrl('get'), data, true, dispatch);
};

const getMeeingLogs = (data, dispatch) => {
  return postRequest('communication', getMeetingUrl('get/all-meetings'), data, true, dispatch);
};

const getMeetingsDetails = (data, dispatch) => {
  return postRequest('communication', getMeetingUrl(`get/${data.id}`), data.payload, true, dispatch);
};

const createMeeting = (data, dispatch) => {
  return postRequest('communication', getMeetingUrl('add'), data, true, dispatch);
};

const editMeeting = (data, dispatch) => {
  return putRequest('communication', getMeetingUrl(`edit/${data.id}`), data.payload, true, dispatch);
};

const deleteMeeting = (data, dispatch) => {
  return deleletRequest('communication', getMeetingUrl(`delete/${data.id}`), data.payload, true, dispatch);
};

const addMinutesOfMeeting = (data, dispatch) => {
  return patchRequest('communication', getMeetingUrl('add/mom'), data, true, dispatch);
};

const sendNotification = (data, dispatch) => {
  return getRequest('communication', `communication/meeting/add/send-reminder/${data?.meetingID}`, data, true, dispatch);
};

const sendAgendaNotification = (data, dispatch) => {
  return getRequest('communication', `communication/meeting/add/send-reminder/${data?.meetingID}/${data?.agendaID}`, data, true, dispatch);
};

const getAssignees = (data, dispatch) => {
  return postRequest('communication', '/communication/meeting/get/assignees', data, true, dispatch);
};

const addAgendas = (data, dispatch) => {
  return postRequest('communication', `/communication/meeting/add/agenda/${data?.meetingID}`, data?.payload || {}, true, dispatch);
};

export const getMeetingsListAction = createGenericAsyncThunk('meeting/getMeetingsListAction', getMeetingsList, 'get', false);
export const getMeetingsDetailsAction = createGenericAsyncThunk('meeting/getMeetingsDetailsAction', getMeetingsDetails, 'get', false);
export const createMeetingAction = createGenericAsyncThunk('meeting/createMeetingAction', createMeeting, 'post', false);
export const editMeetingAction = createGenericAsyncThunk('meeting/editMeetingAction', editMeeting, 'get', false);
export const deleteMeetingAction = createGenericAsyncThunk('meeting/deleteMeetingAction', deleteMeeting, 'get', false);
export const addMinutesOfMeetingAction = createGenericAsyncThunk('meeting/addMinutesOfMeetingAction', addMinutesOfMeeting, 'get', false);
export const sendNotificationAction = createGenericAsyncThunk('meeting/sendNotificationAction', sendNotification, false);
export const sendAgendaNotificationAction = createGenericAsyncThunk('meeting/sendAgendaNotificationAction', sendAgendaNotification, false);
export const getAssigneesAction = createGenericAsyncThunk('meeting/getAssigneesAction', getAssignees, 'get', false);
export const addAgendaAction = createGenericAsyncThunk('meeting/addAgendaAction', addAgendas, false);
export const getMeeingLogsAction = createGenericAsyncThunk('meeting/getMeeingLogsAction', getMeeingLogs, false);
