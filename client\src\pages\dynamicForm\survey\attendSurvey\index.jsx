import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import EventBusyIcon from '@mui/icons-material/EventBusy';
import HourglassTopIcon from '@mui/icons-material/HourglassTop';
import { Box, Button, Card, CardActions, CardContent, CardHeader, Chip, Divider, Grid, Stack, Typography } from '@mui/material';
import Loader from 'components/Loader';
import MainCard from 'components/MainCard';
import { get } from 'lodash';
import ShowHTMLContent from 'pages/component/InputFields/htmlContent';
import PageTitle from 'pages/component/pageTitle';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import { getSurveyToAttendAction } from 'redux/actions/surveyAction';
import { AttendSurveyPageUrl, ViewSurveyResponsePageUrl } from 'utils/constant';
import { convertDateToStringFormat, editPageUrl, formatTimeOnly, getTodayTimestampFromTime, viewPageUrl } from 'utils/helper';
import { useAnalytics } from 'utils/userAnalytics';

const AttendSurveyList = () => {
  const dynamicFormData = useSelector((state) => state.dynamicForm);
  const [surveys, setSurveys] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { trackUserActivity } = useAnalytics();

  const getAllSurvey = async () => {
    await dispatch(getSurveyToAttendAction());
  };

  useEffect(() => {
    getAllSurvey();
    trackUserActivity({
      page: 'Take Survey',
      action: 'page_visit',
      module: 'Survey'
    });
  }, []);

  const checkSurveyStatusByFrequency = (survey, currentTime) => {
    const frequency = survey?.frequency;

    let isStarted = false;
    let isEnded = false;
    let surveyStatus = 'Upcoming...';
    let startTimeFormatted = '';
    let endTimeFormatted = '';

    if (frequency === 'once') {
      const currentDate = new Date(currentTime).toDateString();
      const startDate = new Date(survey?.startDate).toDateString();

      if (currentDate === startDate) {
        const surveyStartTimeToday = getTodayTimestampFromTime(survey?.startTime);
        const surveyEndTimeToday = getTodayTimestampFromTime(survey?.endTime);

        isStarted = currentTime >= surveyStartTimeToday;
        isEnded = currentTime > surveyEndTimeToday;

        startTimeFormatted = new Date(surveyStartTimeToday).toLocaleString();
        endTimeFormatted = new Date(surveyEndTimeToday).toLocaleString();
      } else if (new Date(currentTime) < new Date(survey?.startDate)) {
        isStarted = false;
        isEnded = false;
      } else {
        isStarted = false;
        isEnded = true;
      }
    }

    if (frequency === 'daily') {
      const surveyStartTimeToday = getTodayTimestampFromTime(survey?.startTime);
      const surveyEndTimeToday = getTodayTimestampFromTime(survey?.endTime);

      isStarted = currentTime >= surveyStartTimeToday;
      isEnded = currentTime > surveyEndTimeToday;

      startTimeFormatted = new Date(surveyStartTimeToday).toLocaleString();
      endTimeFormatted = new Date(surveyEndTimeToday).toLocaleString();
    }

    if (isEnded) surveyStatus = 'Ended';
    else if (isStarted) surveyStatus = 'Started';

    return {
      isSurveyStarted: isStarted && !isEnded,
      isSurveyEnded: isEnded,
      surveyStart: surveyStatus,
      startTimeFormatted,
      endTimeFormatted
    };
  };

  useEffect(() => {
    const updatedSurveys = get(dynamicFormData, 'surveyToAttend', []);
    const currentTime = Date.now();

    const enrichSurveys = () => {
      return updatedSurveys.map((survey) => {
        const status = checkSurveyStatusByFrequency(survey, Date.now());
        return {
          ...survey,
          ...status,
          updatedAt: Date.now() // force object reference change
        };
      });
    };

    setSurveys(enrichSurveys());

    const interval = setInterval(() => {
      setSurveys(enrichSurveys());
    }, 30000);

    return () => clearInterval(interval);
  }, [dynamicFormData?.surveyToAttend]);

  const filteredSurveys = surveys.filter((survey) => survey?.name?.toLowerCase().includes(searchQuery.toLowerCase()));

  const getStatusChip = (status) => {
    switch (status) {
      case 'Started':
      case 'Started (Always)':
        return <Chip icon={<EventAvailableIcon />} label="Started" color="success" />;
      case 'Ended':
        return <Chip icon={<EventBusyIcon />} label="Ended" color="error" />;
      default:
        return <Chip icon={<HourglassTopIcon />} label="Upcoming" color="warning" />;
    }
  };
  const timeZoneConfig = JSON.parse(localStorage.getItem('selectedArazCityTimeZone'));

  const FlexTimeDisplay = ({ label, value, isDate }) => (
    <Box display="flex" justifyContent="space-between">
      <Typography variant="body2" fontWeight={500} color="text.secondary">
        {label}
      </Typography>
      <Typography variant="body2">{isDate ? convertDateToStringFormat(value) : formatTimeOnly(value, timeZoneConfig)}</Typography>
    </Box>
  );

  return (
    <>
      {dynamicFormData?.loading && <Loader />}
      <PageTitle title="Take Survey" />

      <Grid container spacing={3}>
        {filteredSurveys.length === 0 ? (
          <Grid item xs={12}>
            <MainCard>
              <Typography variant="h6" textAlign="center" color="secondary" fontWeight={500}>
                No surveys found.
              </Typography>
            </MainCard>
          </Grid>
        ) : (
          filteredSurveys.map((survey) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={survey?._id}>
              <Card
                elevation={4}
                sx={{
                  borderRadius: 4,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                  boxShadow: '0 2px 5px rgba(0, 0, 0, 0.15)',
                  '&:hover': {
                    transform: 'translateY(-5px)'
                  }
                }}
              >
                <CardHeader
                  title={
                    <Box display="flex" justifyContent="space-between" alignItems="center" width={'100%'}>
                      <ShowHTMLContent value={survey?.name} className="take-survey-title" />
                    </Box>
                  }
                  sx={{ pb: 0, mb: 1 }}
                />

                <Divider />

                <CardContent sx={{ flexGrow: 1 }}>
                  <Stack spacing={1.5}>
                    {survey?.frequency && (
                      <Box display="flex" justifyContent="space-between">
                        <Typography variant="caption" color="secondary">
                          {survey?.frequency === 'daily' && 'Attend once per day'}
                          {survey?.frequency === 'once' && 'Attend once per season'}
                        </Typography>
                        {getStatusChip(survey?.surveyStart)}
                      </Box>
                    )}

                    {survey?.frequency === 'once' && (
                      <>
                        <FlexTimeDisplay label="Date" value={survey?.startDate} isDate={true} />
                        <FlexTimeDisplay label="Start Time" value={survey?.startTime} />
                        <FlexTimeDisplay label="End Time" value={survey?.endTime} />
                      </>
                    )}

                    {survey?.frequency === 'daily' && (
                      <>
                        <FlexTimeDisplay label="Start Time" value={survey?.startTime} />
                        <FlexTimeDisplay label="End Time" value={survey?.endTime} />
                      </>
                    )}
                    {/* No time shown for frequency === 'always' */}
                  </Stack>
                </CardContent>

                <CardActions sx={{ justifyContent: 'space-between', px: 3, pb: 2 }}>
                  {survey?.isSurveyStarted ? (
                    <Button
                      variant="contained"
                      size="medium"
                      fullWidth
                      onClick={() => navigate(editPageUrl(AttendSurveyPageUrl, survey?._id))}
                    >
                      Add Response
                    </Button>
                  ) : (
                    <Button
                      variant="outlined"
                      size="medium"
                      fullWidth
                      onClick={() => navigate(viewPageUrl(ViewSurveyResponsePageUrl, survey?._id))}
                    >
                      {survey?.hasResponded ? 'View Response' : 'View Survey'}
                    </Button>
                  )}
                  {/* {survey?.isSurveyStarted ? (
                    survey?.hasResponded ? (
                      <Button
                        variant="contained"
                        size="medium"
                        fullWidth
                        onClick={() => navigate(editPageUrl(AttendSurveyPageUrl, survey?._id))}
                      >
                        Change Response
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        size="medium"
                        fullWidth
                        onClick={() => navigate(editPageUrl(AttendSurveyPageUrl, survey?._id))}
                      >
                        Attend Survey
                      </Button>
                    )
                  ) : (
                    <Button
                      variant="outlined"
                      size="medium"
                      fullWidth
                      onClick={() => navigate(viewPageUrl(ViewSurveyResponsePageUrl, survey?._id))}
                    >
                      {survey?.hasResponded ? 'View Response' : 'View Survey'}
                    </Button>
                  )} */}
                </CardActions>
              </Card>
            </Grid>
          ))
        )}
      </Grid>
    </>
  );
};

export default AttendSurveyList;
