const express = require("express");
const cors = require("cors");
const path = require("path");
const { CORS_ORIGIN, NODE_ENV, AUTH_SERVICE_PORT } = require("../constants");
const routes = require("../modules/user/routes");
const { authGuard, roleGuard } = require("../middlewares/guard.middleware");
const { apiLoggerMiddleware, apiErrorLoggerMiddleware, attachStartTime } = require("../middlewares/apiLogger.middleware");
const { addServiceHeader } = require("../middlewares/serviceHeader.middleware");
const { connectDB } = require("../db");

const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));

// Add service header middleware
app.use(addServiceHeader('auth'));

// Add API logging middleware
app.use(attachStartTime);
app.use(apiLoggerMiddleware);

app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});

app.use("/api", authGuard, roleGuard, routes);

app.get("/", (req, res) => {
  res.send(`Authentication Service is running on port: ${AUTH_SERVICE_PORT}`);
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    service: "authentication",
    port: AUTH_SERVICE_PORT,
    timestamp: new Date().toISOString(),
  });
});

// Add error logging middleware at the end
app.use(apiErrorLoggerMiddleware);

// Start server
const startServer = async () => {
  try {
    await connectDB();
    const server = app.listen(AUTH_SERVICE_PORT, () => {
      console.log(`Node environment: ${NODE_ENV}`);
      console.log(
        `Authentication Service running on port: ${AUTH_SERVICE_PORT}`
      );
    });

    // Graceful shutdown
    const gracefulShutdown = (signal) => {
      console.log(`\n📴 Received ${signal}. Shutting down auth service gracefully...`);

      server.close((err) => {
        if (err) {
          console.error('❌ Error during server shutdown:', err);
          process.exit(1);
        }

        console.log('✅ Auth service closed successfully');
        process.exit(0);
      });

      // Force shutdown after 10 seconds
      setTimeout(() => {
        console.error('⚠️  Forced shutdown after timeout');
        process.exit(1);
      }, 10000);
    };

    // Handle shutdown signals
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGHUP', () => gracefulShutdown('SIGHUP'));

  } catch (err) {
    console.log(`DB Error: ${err.message}`);
    process.exit(1);
  }
};

if (require.main === module) {
  startServer();
}

module.exports = app;
