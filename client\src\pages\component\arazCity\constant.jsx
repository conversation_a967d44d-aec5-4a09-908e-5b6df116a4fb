import * as yup from 'yup';
export const ArazCityFilters = [
  { type: 'search', name: 'search', label: 'Search', placeholder: 'Search...' },
  {
    type: 'select',
    name: 'miqaatName',
    searchKey: 'miqaatName',
    label: 'Select Miqaat',
    placeholder: 'Select Miqaat',
    items: []
  }
];

export const ArazCitySchema = yup.object().shape({
  miqaatID: yup.string().required('Miqaat Name is required'),
  name: yup.string().trim().required('Araz City Name is required'),
  jamiats: yup.array().of(yup.string()).min(1, 'Jamiat is required').required('Jamiat is required'),
  jamaats: yup.array().of(yup.string()).min(1, 'Jamaat is required').required('Jamaat is required')
});

export const ArazCityDefaultValues = {
  miqaatID: '',
  name: '',
  jamiats: [],
  fasalDate: null,
  isFasal: false,
  jamaats: [],
  logo: null,
  addToOpenProject: false,
  showPositionAlias: false
};

export const ArazCityFields = [
  {
    name: 'miqaatID',
    label: 'Miqaat',
    type: 'select',
    placeholder: 'Select Miqaat',
    md: 6,
    items: []
  },
  {
    name: 'status',
    label: 'Status',
    type: 'radio',
    md: 6,
    row: true,
    defaultValue: 'active',
    items: [
      { label: 'Active', value: true },
      { label: 'Inactive', value: false }
    ]
  },
  {
    name: 'name',
    label: 'Araz City Name',

    md: 6,
    type: 'text'
  },
  {
    name: 'LDName',
    label: 'Araz City Name (LD)',
    md: 6,
    type: 'ldField'
  },
  {
    name: 'jamiats',
    label: 'Jamiat',
    type: 'autoselect',
    placeholder: 'Select Jamiat',
    multiple: true,
    addAllOption: true,
    md: 6,
    items: []
  },
  {
    name: 'jamaats',
    label: 'Jamaat',
    md: 6,
    placeholder: 'Select Jamaat',
    type: 'autoselect',
    multiple: true,
    addAllOption: true,
    items: []
  },
  { name: 'fasalDate', md: 6, label: 'Fasal Date', placeholder: 'Select Fasal Date', type: 'date' },
  {
    name: 'timeZoneID',
    label: 'Time Zone',
    type: 'select',
    placeholder: 'Select Time Zone',
    md: 6,
    items: []
  },
  {
    name: 'isFasal',
    placeholder: 'Is Fasal City',
    label: 'Is Fasal City',
    hideField: true,
    md: 6,
    type: 'checkbox',
    isEdit: false
  },

  {
    name: 'logo',
    label: 'Araz City Logo',
    md: 12,
    placeholder: 'Drop and Select files ( GIF/JPEF Files )',
    type: 'image-input'
  },
  {
    name: 'addToOpenProject',
    placeholder: 'Add to Open Project',
    md: 4,
    type: 'checkbox',
    isEdit: false
  },
  {
    name: 'showPositionAlias',
    placeholder: 'Use Hierarchy Position Alias',
    md: 4,
    type: 'checkbox'
  }
];

export const ArazCityReportTabs = [
  { label: 'Jamaat Wise', value: 'jamaatWise' },
  { label: '12 Umoor Khidmat', value: 'umoorKhidmatWise' },
  { label: 'Qualifications', value: 'qualificationWise' },
  { label: 'Occupations', value: 'occupationWise' },
  { label: 'Zone Wise', value: 'zonalWise' }
];

export const jamaatWiseHeaders = [
  {
    name: 'name',
    type: 'text',
    backgroundColor: '#EEEEEE',
    backgroundColor: '#EEEEEE',
    title: 'Jamaat Name',
    sortingactive: true,
    minWidth: '70px'
  },
  { name: 'mardo.count', type: 'text', backgroundColor: '#EEEEEE', title: 'Mardo Count', sortingactive: true, minWidth: '50px' },
  { name: 'bairao.count', type: 'text', backgroundColor: '#EEEEEE', title: 'Bairao Count', sortingactive: true, minWidth: '50px' },
  {
    name: 'gairBalighDikrao.count',
    type: 'text',
    backgroundColor: '#EEEEEE',
    title: 'Gair Baligh (Dikrao) Count',
    sortingactive: true,
    minWidth: '80px'
  },
  {
    name: 'gairBalighDikrio.count',
    type: 'text',
    backgroundColor: '#EEEEEE',
    title: 'Gair Baligh (Dikrio) Count',
    sortingactive: true,
    minWidth: '80px'
  },
  { name: 'totalMumineen', type: 'text', backgroundColor: '#EEEEEE', title: 'Total', sortingactive: true, minWidth: '50px' }
];

export const umoorKhidmatWiseHeaders = [
  { name: 'name', type: 'text', backgroundColor: '#EEEEEE', title: '12 Umoor Name', sortingactive: true, minWidth: '70px' },
  { name: 'mardo.count', type: 'text', backgroundColor: '#EEEEEE', title: 'Mardo Count', sortingactive: true, minWidth: '80px' },
  { name: 'bairao.count', type: 'text', backgroundColor: '#EEEEEE', title: 'Bairao Count', sortingactive: true, minWidth: '210px' },
  { name: 'totalMumineen', type: 'text', backgroundColor: '#EEEEEE', title: 'Total', sortingactive: true, minWidth: '50px' }
];

export const qualifiationWiseHeaders = [
  { name: 'name', type: 'text', backgroundColor: '#EEEEEE', title: 'Qualifications', sortingactive: true, minWidth: '70px' },
  { name: 'mardo.count', type: 'text', backgroundColor: '#EEEEEE', title: 'Mardo Count', sortingactive: true, minWidth: '80px' },
  { name: 'bairao.count', type: 'text', backgroundColor: '#EEEEEE', title: 'Bairao Count', sortingactive: true, minWidth: '210px' },
  { name: 'totalMumineen', type: 'text', backgroundColor: '#EEEEEE', title: 'Total', sortingactive: true, minWidth: '50px' }
];

export const occupationsWiseHeaders = [
  { name: 'name', type: 'text', backgroundColor: '#EEEEEE', title: 'Occupations', sortingactive: true, minWidth: '70px' },
  { name: 'mardo.count', type: 'text', backgroundColor: '#EEEEEE', title: 'Mardo Count', sortingactive: true, minWidth: '80px' },
  { name: 'bairao.count', type: 'text', backgroundColor: '#EEEEEE', title: 'Bairao Count', sortingactive: true, minWidth: '210px' },
  { name: 'totalMumineen', type: 'text', backgroundColor: '#EEEEEE', title: 'Total', sortingactive: true, minWidth: '50px' }
];

export const zoneWiseHeaders = [
  { name: 'zoneName', type: 'text', backgroundColor: '#EEEEEE', title: 'Zone Name', sortingactive: true, minWidth: '70px' },
  { name: 'mardoCount', type: 'text', backgroundColor: '#EEEEEE', title: 'Mardo Count', sortingactive: true, minWidth: '80px' },
  { name: 'bairaoCount', type: 'text', backgroundColor: '#EEEEEE', title: 'Bairao Count', sortingactive: true, minWidth: '80px' },
  {
    name: 'gairBalighDikraoCount',
    type: 'text',
    backgroundColor: '#EEEEEE',
    title: 'Gair Baligh (Dikrao) Count',
    sortingactive: true,
    minWidth: '80px'
  },
  {
    name: 'gairBalighDikrioCount',
    type: 'text',
    backgroundColor: '#EEEEEE',
    title: 'Gair Baligh (Dikrio) Count',
    sortingactive: true,
    minWidth: '80px'
  },
  { name: 'total', type: 'text', backgroundColor: '#EEEEEE', title: 'Total', sortingactive: true, minWidth: '50px' }
];

export const reportSummaryCard = [
  // { name: 'jamaatCount', label: 'Total Jamaat' },
  { name: 'totalMumineen', label: 'Total Mumineen' }
];
