import React, { useEffect, useState } from 'react';
import { HierarchyArazCityPageUrl, SUPERADMIN } from 'utils/constant';
import CustomForm from 'pages/component/form';
import { useNavigate, useParams } from 'react-router';
import { ArazCityDefaultValues, ArazCityFields, ArazCitySchema } from './constant';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useDispatch, useSelector } from 'react-redux';
import {
  addArazCityAction,
  getAllJamaatsAction,
  getAllJamiatsAction,
  getSingleArazCityAction,
  getTimeZoneAction,
  updateArazCityAction
} from 'redux/actions/arazCityAction';
import { getAllMiqaatsAction, getMiqaatsAction } from 'redux/actions/miqaatActions';
import { cloneDeep, get, isEmpty } from 'lodash';
import { setAllJamatAction, setSingleArazCity } from 'redux/reducers/arazCityReducer';
import { getFieldValues, getPageUrl } from 'utils/helper';
import { getUserDetail } from 'utils/auth';
import { useAnalytics } from 'utils/userAnalytics';

const AddEditArazCity = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { id: arazCityID } = useParams();
  const arazCityData = useSelector((state) => state.arazCity);
  const miqaatData = useSelector((state) => state.miqaats);
  const [modifiedFieldValue, setModifiedFields] = useState(ArazCityFields);
  const { trackUserActivity } = useAnalytics();

  console.log('TimeZoneData', arazCityData);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    getValues
  } = useForm({
    resolver: yupResolver(ArazCitySchema),
    defaultValues: ArazCityDefaultValues
  });

  useEffect(() => {
    if (arazCityID) {
      dispatch(getAllMiqaatsAction('all'));
    } else {
      dispatch(getAllMiqaatsAction('active'));
    }
    dispatch(getTimeZoneAction());
    dispatch(getAllJamiatsAction());
    trackUserActivity({
      action: 'page_visit',
      page: `${arazCityID ? 'Edit Araz City' : 'Add Araz City'}`,
      module: 'Global Masters'
    });
  }, []);

  useEffect(() => {
    let { singleArazCity } = arazCityData || {};

    if (arazCityID && singleArazCity) {
      let filteredData = getFieldValues(singleArazCity, modifiedFieldValue);

      if (get(singleArazCity, 'jamiats', [])?.length > 0) {
        dispatch(getAllJamaatsAction({ ids: get(singleArazCity, 'jamiats', []) }));
      }

      if (filteredData.timeZoneID && typeof filteredData.timeZoneID === 'object') {
        filteredData.timeZoneID = filteredData.timeZoneID._id || filteredData.timeZoneID.value || '';
      }
      reset({ ...filteredData, showPositionAlias: singleArazCity?.showPositionAlias || false });
      // const fields = [...modifiedFieldValue].filter((item) => item.name !== 'addToOpenProject');
      // setModifiedFields([...fields]);
    } else {
      dispatch(setAllJamatAction([]));
    }
  }, [arazCityData?.singleArazCity]);

  const updateFieldsWithOptions = () => {
    let { role } = getUserDetail();
    let updatedFields = cloneDeep(modifiedFieldValue);
    let { miqaats } = miqaatData || {};
    let { allJamaats, allJamiats } = arazCityData || {};
    let { timeZoneData } = arazCityData || {};

    if (!isEmpty(timeZoneData)) {
      updatedFields = updateFields(updatedFields, timeZoneData, 'timeZoneID', 'label');
    }
    if (!isEmpty(miqaats)) {
      updatedFields = updateFields(updatedFields, miqaats, 'miqaatID', 'name');
    }
    if (!isEmpty(allJamiats)) {
      updatedFields = updateFields(updatedFields, allJamiats, 'jamiats', 'name');
    }
    if (!isEmpty(allJamaats)) {
      updatedFields = updateFields(updatedFields, allJamaats, 'jamaats', 'name');
    }
    if (!isEmpty(arazCityData?.singleArazCity)) {
      updatedFields = updatedFields?.filter((item) => item.name !== 'addToOpenProject');
    }
    updatedFields = updatedFields.map((item) => {
      return item.name === 'isFasal' && role === SUPERADMIN ? { ...item, hideField: false } : item;
    });

    setModifiedFields(updatedFields);
  };

  useEffect(() => {
    updateFieldsWithOptions();
  }, [miqaatData?.miqaats, arazCityData?.allJamiats, arazCityData?.allJamaats, arazCityData?.singleArazCity]);

  useEffect(() => {
    if (arazCityID) {
      dispatch(getSingleArazCityAction(arazCityID));
    }
  }, [arazCityID]);

  const updateFields = (fields, options, targetFieldName, key) => {
    return fields?.map((field) => {
      if (field.name === targetFieldName) {
        let updateOptions = options?.map((option) => ({
          label: option[key],
          value: option?._id
        }));
        return {
          ...field,
          items: field?.multiple && updateOptions?.length > 0 ? [{ label: 'All', value: 'all' }, ...updateOptions] : updateOptions
        };
      }
      return field;
    });
  };

  const resetJamaats = (value) => {
    const currentValues = getValues();
    reset({
      ...currentValues,
      jamiats: value,
      jamaats: []
    });
  };

  const handleInputChange = (type, name, value) => {
    switch (name) {
      case 'isFasal':
        reset({ ...getValues(), [name]: value, fasalDate: null });
        break;
      case 'jamiats':
        if (value?.some((item) => item === 'all')) {
          resetJamaats(['all']);
          dispatch(getAllJamaatsAction({ ids: ['all'] }));
        } else {
          if (value?.length > 0) {
            resetJamaats(value);
            dispatch(getAllJamaatsAction({ ids: value }));
          } else {
            dispatch(setAllJamatAction([]));
            resetJamaats([]);
          }
        }
        break;

      case 'jamaats':
        if (value?.some((item) => item === 'all')) {
          const currentValues = getValues();
          reset({
            ...currentValues,
            jamaats: ['all']
          });
        }
        break;
      case 'status':
        reset({ ...getValues(), status: value === true || value === 'true' });
        break;
      default:
        break;
    }
  };

  const onSubmit = async (data) => {
    let response = {};
    const { fasalDate, ...rest } = data || {};
    let payload = { ...rest, id: arazCityID };

    if (data?.logo && data?.logo?.length > 0) {
      payload = {
        ...payload,
        logo: data?.logo[0]
      };
    }
    if (data?.isFasal) {
      payload = {
        ...payload,
        fasalDate: data?.fasalDate
      };
    }
    if (arazCityID) {
      response = await dispatch(updateArazCityAction({ ...payload, id: arazCityID }));
    } else {
      response = await dispatch(addArazCityAction(payload));
    }

    if (get(response, 'payload.success', false)) {
      navigate(getPageUrl(HierarchyArazCityPageUrl));
    }
  };

  const handleCancel = () => {
    navigate(getPageUrl(HierarchyArazCityPageUrl));
    dispatch(setSingleArazCity({}));
  };

  return (
    <CustomForm
      title={arazCityID ? 'Edit Araz City' : 'Add Araz City'}
      loading={arazCityData?.loading}
      pageUrl={getPageUrl(HierarchyArazCityPageUrl)}
      fields={modifiedFieldValue}
      handleInputChange={(type, name, value) => handleInputChange(type, name, value)}
      control={control}
      errors={errors}
      handleSubmit={handleSubmit(onSubmit)}
      handleCancel={handleCancel}
    />
  );
};

export default AddEditArazCity;
