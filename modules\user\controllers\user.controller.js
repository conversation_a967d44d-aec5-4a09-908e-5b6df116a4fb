const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON>rror,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  NOT_FOUND,
  CUSTOM_ERROR,
  FORBIDDEN,
  SUCCESS,
} = require("../../../utils/message.util");
const { isEmpty, toObjectId } = require("../../../utils/misc.util");
const { KGUser, SystemRole, ArazCity } = require("../models");
const { Interest } = require("../../hierarchy/models/interest.model");
const { getITSUserData } = require("../../../utils/ITSHelper.util");
const { processDecryptedData } = require("../../../utils/decryption.util");
const {
  ITS_TOKEN_KEY,
  JWT_SECRET,
  CRYPTO_SECRET,
  SYSTEM_ROLES,
} = require("../../../constants");
const jwt = require("jsonwebtoken");
const CryptoJS = require("crypto-js");
const constants = require("../../../constants");
const { RazaMapping } = require("../../zonesCapacity/models");
const { redisCacheKeys, getCache, setCache } = require("../../../utils/redis.cache");

const fetchSystemUsers = async (ITSID) => {
  try {
    const systemUserData = await KGUser.findOne({ ITSID })
      .populate("miqaats.miqaatID", "name status")
      .populate("miqaats.arazCityID", "name status")
      .populate("miqaats.hierarchyPositionID", "name uniqueName alias")
      .populate("miqaats.arazCityZoneID", "name uniqueName");

    userData = {
      id: systemUserData?._id,
      name: systemUserData?.name,
      ITSID: systemUserData?.ITSID,
      logo: systemUserData?.logo,
      miqaats: systemUserData?.miqaats?.filter(
        (miqaat) =>
          !isEmpty(miqaat.miqaatID) &&
          !isEmpty(miqaat.arazCityID) &&
          !isEmpty(miqaat.cityRoleID) &&
          miqaat.isActive &&
          miqaat.arazCityID.status
      ),
      systemRoleID: systemUserData?.systemRoleID,
      jamiatID: systemUserData?.jamiatID,
      jamaatID: systemUserData?.jamaatID,
      treatAsCityUser: systemUserData?.treatAsCityUser,
    };
    return {
      success: true,
      message: "System user fetched successfully",
      data: userData,
    };
  } catch (error) {
    return null;
  }
};

async function getSystemUserMiqaats() {
  const cities = await ArazCity.find(
    { status: true },
    "_id name miqaatID jamiats jamaats"
  )
    .populate("miqaatID", "_id name isActive")
    .sort({ _id: -1 });

  const map = new Map();

  for (const { miqaatID, _id, name, jamiats, jamaats } of cities) {
    if (!map.has(miqaatID._id)) {
      map.set(miqaatID._id, {
        _id: miqaatID._id,
        name: miqaatID.name,
        arazCities: [],
      });
    }

    map.get(miqaatID._id).arazCities.push({ _id, name, jamiats, jamaats });
  }

  return Array.from(map.values());
}

async function findMatchingMiqaatCity(
  systemUserMiqaats,
  jamiatID,
  jammatID,
  ITSID,
  isUserFromITS
) {
  for (const miqaat of systemUserMiqaats) {
    for (const city of miqaat.arazCities) {
      if (
        city.jamiats?.includes(jamiatID.toString()) &&
        city.jamaats?.includes(jammatID.toString())
      ) {
        return {
          miqaatID: miqaat._id,
          arazCityID: city._id,
        };
      }
    }
  }

  // if (isUserFromITS) {
  const foundUser = await RazaMapping.aggregate([
    {
      $match: {
        ITSID,
        RazaStatus: "Has Raza",
      },
    },
    {
      $lookup: {
        from: "miqaats",
        localField: "miqaatID",
        foreignField: "_id",
        as: "miqaatID",
      },
    },
    {
      $unwind: {
        path: "$miqaatID",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        "miqaatID.status": "active",
      },
    },
    {
      $lookup: {
        from: "arazcities",
        localField: "arazCityID",
        foreignField: "_id",
        as: "arazCityID",
      },
    },
    {
      $unwind: {
        path: "$arazCityID",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        "arazCityID.status": true,
      },
    },
    { $sort: { createdAt: -1 } },
  ]).exec();

  if (foundUser.length !== 0) {
    return {
      multiple: foundUser.length > 1,
      razaStatus: foundUser[0].RazaStatus,
      miqaatZone: foundUser[0].MiqaatZone,
      miqaatID: foundUser[0].miqaatID,
      arazCityID: foundUser[0].arazCityID,
    };
  }
  // }

  return null;
}

const handleNotAssignedUser = async (res, user, match, isNewUser, ITSID) => {
  const newMiqaat = {
    miqaatID: match.miqaatID,
    arazCityID: match.arazCityID,
    cityRoleID: SYSTEM_ROLES.NOT_ASSIGNED[0],
  };
  // const razaStatus = await RazaMapping.findOne({
  //   miqaatID: match.miqaatID,
  //   arazCityID: match.arazCityID,
  //   ITSID: ITSID,
  // });
  // if (!isEmpty(razaStatus)) {
  //   newMiqaat.miqaatHR = {
  //     RazaStatus: razaStatus.RazaStatus,
  //     MiqaatZone: razaStatus.MiqaatZone,
  //   };
  // }
  if (match.razaStatus === "Has Raza") {
    newMiqaat.miqaatHR = {
      RazaStatus: match.razaStatus,
      MiqaatZone: match.miqaatZone,
    };
  }

  let userData = {};

  if (isNewUser) {
    user.miqaats = [newMiqaat];
    await KGUser.create(user);
  } else {
    userData = await KGUser.findOne({ ITSID });
    const matchedMiqaat = userData.miqaats.find(
      (m) =>
        m.miqaatID?.toString() === match.miqaatID?._id?.toString() &&
        m.arazCityID?.toString() === match.arazCityID?._id?.toString()
    );

    if (!matchedMiqaat) {
      userData.miqaats.push(newMiqaat);
      await KGUser.findOneAndUpdate({ ITSID }, { miqaats: userData.miqaats });
    }
  }

  userData = await KGUser.findOne({ ITSID })
    .populate("jamiatID", "name")
    .populate("jamaatID", "name")
    .populate("miqaats.miqaatID", "name status")
    .populate("miqaats.arazCityID", "name status")
    .populate("miqaats.hierarchyPositionID", "name uniqueName alias")
    .populate("miqaats.arazCityZoneID", "name uniqueName")
    .lean();

  if (!isNewUser) {
    userData.interestData = await Interest.findOne({ ITSID });
  }

  userData.id = userData._id;
  userData.notAssignedArazCityID = match.arazCityID;
  userData.notAssignedMiqaatID = match.miqaatID;
  const systemRoleData = await SystemRole.findById(
    toObjectId(SYSTEM_ROLES.NOT_ASSIGNED[0])
  ).select("uniqueName modules");

  const encryptedSystemRole = CryptoJS.AES.encrypt(
    JSON.stringify({
      systemRoleName: systemRoleData?.uniqueName,
      modules: systemRoleData?.modules,
    }),
    CRYPTO_SECRET
  ).toString();
  const encryptedSystemRoleID = CryptoJS.AES.encrypt(
    JSON.stringify({
      systemRoleID: systemRoleData?._id,
    }),
    CRYPTO_SECRET
  ).toString();

  const token = jwt.sign(userData, JWT_SECRET, { expiresIn: "7d" });

  return apiResponse(
    SUCCESS,
    "ITS User",
    {
      invalidAccess: true,
      message: "You're not available in any miqaat and Araz City",
      notAssigned: true,
      systemRole: encryptedSystemRole,
      systemRoleID: encryptedSystemRoleID,
      token,
    },
    res
  );
};

const handleRazaMiqaats = async (ITSID) => {
  const foundUser = await RazaMapping.aggregate([
    {
      $match: {
        ITSID,
        RazaStatus: "Has Raza",
      },
    },
    {
      $lookup: {
        from: "miqaats",
        localField: "miqaatID",
        foreignField: "_id",
        as: "miqaatID",
      },
    },
    {
      $unwind: {
        path: "$miqaatID",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        "miqaatID.status": "active",
      },
    },
    {
      $lookup: {
        from: "arazcities",
        localField: "arazCityID",
        foreignField: "_id",
        as: "arazCityID",
      },
    },
    {
      $unwind: {
        path: "$arazCityID",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        "arazCityID.status": true,
      },
    },
    { $sort: { createdAt: -1 } },
  ]).exec();

  if (foundUser.length === 0) {
    return null;
  }

  const existingUser = await KGUser.findOne({ ITSID });

  if (isEmpty(existingUser)) {
    return null;
  }
  const existingMiqaats = existingUser?.miqaats || [];

  const newMiqaats = [];
  const updates = [];

  for (const miqaat of foundUser) {
    const matchIndex = existingMiqaats.findIndex(
      (existing) =>
        existing.miqaatID?.toString() === miqaat.miqaatID?._id?.toString() &&
        existing.arazCityID?.toString() === miqaat.arazCityID?._id?.toString()
    );

    if (matchIndex === -1) {
      // New miqaat
      newMiqaats.push({
        isActive: true,
        miqaatID: miqaat.miqaatID,
        arazCityID: miqaat.arazCityID,
        cityRoleID: SYSTEM_ROLES.NOT_ASSIGNED[0],
        miqaatHR: {
          MiqaatZone: miqaat.MiqaatZone,
          RazaStatus: miqaat.RazaStatus,
        },
      });
    } else {
      // Existing miqaat, check if miqaatHR needs update
      const existing = existingMiqaats[matchIndex];
      if (
        existing.miqaatHR?.MiqaatZone !== miqaat.MiqaatZone ||
        existing.miqaatHR?.RazaStatus !== miqaat.RazaStatus
      ) {
        updates.push({
          index: matchIndex,
          updatedHR: {
            "miqaats.$[elem].miqaatHR.MiqaatZone": miqaat.MiqaatZone,
            "miqaats.$[elem].miqaatHR.RazaStatus": miqaat.RazaStatus,
          },
          filter: {
            "elem.miqaatID": miqaat.miqaatID,
            "elem.arazCityID": miqaat.arazCityID,
          },
        });
      }
    }
  }

  // Perform updates to miqaatHR if needed
  for (const update of updates) {
    await KGUser.updateOne(
      {
        ITSID,
      },
      {
        $set: update.updatedHR,
      },
      {
        arrayFilters: [
          {
            "elem.miqaatID": update.filter["elem.miqaatID"],
            "elem.arazCityID": update.filter["elem.arazCityID"],
          },
        ],
      }
    );
  }

  // Add new miqaats if any
  let updatedUser;
  if (newMiqaats.length > 0) {
    updatedUser = await KGUser.findOneAndUpdate(
      { ITSID },
      { $addToSet: { miqaats: { $each: newMiqaats } } },
      { new: true }
    );
  }
  userData = await KGUser.findOne({ ITSID })
    .populate("jamiatID", "name")
    .populate("jamaatID", "name")
    .populate("miqaats.miqaatID", "name status")
    .populate("miqaats.arazCityID", "name status")
    .populate("miqaats.hierarchyPositionID", "name uniqueName alias")
    .populate("miqaats.arazCityZoneID", "name uniqueName")
    .lean();
  // userData.id = userData._id;
  const result = {
    id: userData?._id,
    name: userData?.name,
    ITSID: userData?.ITSID,
    logo: userData?.logo,
    miqaats: userData?.miqaats?.filter(
      (miqaat) =>
        !isEmpty(miqaat.miqaatID) &&
        !isEmpty(miqaat.arazCityID) &&
        !isEmpty(miqaat.cityRoleID) &&
        miqaat.isActive &&
        miqaat.arazCityID.status
    ),
    systemRoleID: userData?.systemRoleID,
    jamiatID: userData?.jamiatID,
    jamaatID: userData?.jamaatID,
    treatAsCityUser: userData?.treatAsCityUser,
  };
  return result;
};

const loginUserV3 = apiHandler(async (req, res) => {
  const ITSID = await processDecryptedData(ITS_TOKEN_KEY, req.body.DT);
  if (isEmpty(ITSID)) {
    return apiError(CUSTOM_ERROR, "ITS ID could not be fetched", null, res);
  }

  let [userData, systemUserMiqaats] = await Promise.all([
    fetchSystemUsers(ITSID),
    getSystemUserMiqaats(),
  ]);
  if (userData?.data && userData.data?.miqaats?.length) {
    userData.data.miqaats = userData.data.miqaats.filter((miqaat) => {
      return (
        miqaat?.miqaatID?.status === "active" &&
        miqaat?.arazCityID?.status === true
      );
    });
  }

  if (isEmpty(userData?.data)) {
    const ITSUserData = await getITSUserData(ITSID);

    if (isEmpty(ITSUserData)) {
      return apiResponse(
        FORBIDDEN,
        "ITS User",
        {
          invalidAccess: true,
          message: "You're not available in any miqaat and Araz City",
        },
        res
      );
    }

    const match = await findMatchingMiqaatCity(
      systemUserMiqaats,
      ITSUserData?.jamiatID,
      ITSUserData?.jamaatID,
      ITSUserData?.ITSID,
      true
    );

    if (isEmpty(match)) return apiError(FORBIDDEN, "User", null, res);
    if (!match.multiple) {
      return handleNotAssignedUser(res, ITSUserData, match, true, ITSID);
    } else {
      await KGUser.create(ITSUserData);
    }
  }

  const razaData = await handleRazaMiqaats(ITSID);
  if (razaData) {
    userData.data = razaData;
  }

  let { treatAsCityUser, systemRoleID, miqaats } = userData.data;
  const isSystemUser =
    (!isEmpty(systemRoleID) && !treatAsCityUser) ||
    (systemRoleID &&
      systemRoleID.toString() ===
      constants.SYSTEM_ROLES.SUPER_ADMIN[0].toString());

  const data = {
    invalidAccess: false,
    multiple: miqaats.length > 1,
    isSystemUser,
  };

  let roleID = null;

  if (isSystemUser) {
    data.systemUserMiqaats = systemUserMiqaats;
    roleID = systemRoleID;
  } else {
    if (miqaats.length === 1) {
      if (
        miqaats[0]?.cityRoleID.toString() ===
        constants.SYSTEM_ROLES.NOT_ASSIGNED[0].toString()
      ) {
        const match = await findMatchingMiqaatCity(
          systemUserMiqaats,
          userData?.data?.jamiatID.toString(),
          userData?.data?.jamaatID.toString(),
          userData?.data?.ITSID
        );
        if (isEmpty(match)) return apiError(FORBIDDEN, "User", null, res);
        if (!match.multiple) {
          return handleNotAssignedUser(res, userData.data, match, false, ITSID);
        } else {
          const result = await handleRazaMiqaats(ITSID);
          if (result) {
            data.multiple = result.miqaats.length;
            userData.data = result
          }
        }
      } else {
        roleID = miqaats[0]?.cityRoleID;
      }
    } else if (miqaats.length === 0) {
      const match = await findMatchingMiqaatCity(
        systemUserMiqaats,
        userData?.data?.jamiatID.toString(),
        userData?.data?.jamaatID.toString(),
        userData?.data?.ITSID
      );
      if (isEmpty(match)) return apiError(FORBIDDEN, "User", null, res);
      if (!match.multiple) {
        return handleNotAssignedUser(res, userData.data, match, false, ITSID);
      } else {
        const result = await handleRazaMiqaats(ITSID);
        if (result) {
          data.multiple = result.miqaats.length;
          userData.data = result;
        }
      }
    }
  }

  if (roleID !== null) {
    const systemRoleData = await SystemRole.findById(toObjectId(roleID)).select(
      "uniqueName modules"
    );

    data.systemRole = CryptoJS.AES.encrypt(
      JSON.stringify({
        systemRoleName: systemRoleData?.uniqueName,
        modules: systemRoleData?.modules,
      }),
      CRYPTO_SECRET
    ).toString();
    data.systemRoleID = CryptoJS.AES.encrypt(
      JSON.stringify({
        systemRoleID: systemRoleData?._id,
      }),
      CRYPTO_SECRET
    ).toString();
    data.systemRoleModules = CryptoJS.AES.encrypt(
      JSON.stringify({
        systemRoleModules: systemRoleData?.modules,
      }),
      CRYPTO_SECRET
    ).toString();

    if (!isSystemUser && miqaats.length === 1) {
      data.permissions = false;
      data.multiple = false;
    }
  }

  userData.data.interestData = { status: "assigned" }; // TODO: need to check working with not-assigned
  userData.data.isSystemUser = data.isSystemUser;

  data.token = jwt.sign(userData.data, JWT_SECRET, { expiresIn: "7d" });

  return apiResponse(FETCH, "ITS User", data, res);
});


const getUserProfile = apiHandler(async (req, res) => {
  const data = req.user;

  const cacheKey = `${redisCacheKeys.KG_USER}:${data._id}`;
  let cachedData = await getCache(cacheKey);
  if (!isEmpty(cachedData)) {
    return apiResponse(FETCH, "User", cachedData, res, true);
  }

  let userData = await KGUser.aggregate([
    { $match: { _id: toObjectId(data._id) } },
    {
      $lookup: {
        from: "systemroles",
        localField: "systemRoleID",
        foreignField: "_id",
        as: "systemRole",
      },
    },
    { $unwind: { path: "$systemRole", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "jamiats",
        localField: "jamiatID",
        foreignField: "_id",
        as: "jamiat",
      },
    },
    { $unwind: { path: "$jamiat", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "jamaats",
        localField: "jamaatID",
        foreignField: "_id",
        as: "jamaat",
      },
    },
    { $unwind: { path: "$jamaat", preserveNullAndEmptyArrays: true } },
    { $unwind: { path: "$miqaats", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "miqaats.arazCityZoneID",
        foreignField: "_id",
        as: "zone",
      },
    },
    { $unwind: { path: "$zone", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "arazcities",
        localField: "miqaats.arazCityID",
        foreignField: "_id",
        as: "arazCity",
      },
    },
    { $unwind: { path: "$arazCity", preserveNullAndEmptyArrays: true } },
    // { $match: { "arazCity.status": true } },
    {
      $lookup: {
        from: "departments",
        localField: "miqaats.departmentID",
        foreignField: "_id",
        as: "department",
      },
    },
    { $unwind: { path: "$department", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "miqaats",
        localField: "miqaats.miqaatID",
        foreignField: "_id",
        as: "miqaat",
      },
    },
    { $unwind: { path: "$miqaat", preserveNullAndEmptyArrays: true } },
    // { $match: { "miqaat.status": "active" } },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPosition",
      },
    },
    {
      $unwind: { path: "$hierarchyPosition", preserveNullAndEmptyArrays: true },
    },
    { $match: { "miqaats.hierarchyPositionID": { $ne: null } } },
    // { $match: { "miqaats.isActive": true } },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaats.kgTypeID",
        foreignField: "_id",
        as: "kgType",
      },
    },
    { $unwind: { path: "$kgType", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "kggroups",
        localField: "miqaats.kgGroupID",
        foreignField: "_id",
        as: "kgGroup",
      },
    },
    { $unwind: { path: "$kgGroup", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "functions",
        localField: "miqaats.functionID",
        foreignField: "_id",
        as: "function",
      },
    },
    { $unwind: { path: "$function", preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        "miqaats.miqaat": {
          id: "$miqaat._id",
          name: "$miqaat.name",
          status: "$miqaat.status",
        },
        "miqaats.arazCity": {
          id: "$arazCity._id",
          name: "$arazCity.name",
          showPositionAlias: "$arazCity.showPositionAlias",
          status: "$arazCity.status",
        },
        "miqaats.zone": {
          id: "$zone._id",
          name: "$zone.name",
        },
        "miqaats.department": {
          id: "$department._id",
          name: "$department.name",
        },
        "miqaats.kgType": {
          id: "$kgType._id",
          name: "$kgType.name",
        },
        "miqaats.kgGroup": {
          id: "$kgGroup._id",
          name: "$kgGroup.name",
        },
        "miqaats.hierarchyPosition": {
          id: "$hierarchyPosition._id",
          name: "$hierarchyPosition.name",
          alias: "$hierarchyPosition.alias",
        },
        "miqaats.razaStatus": {
          $ifNull: ["$miqaats.miqaatHR", null],
        },
        "miqaats.function": {
          id: "$function._id",
          name: "$function.name",
        },
      },
    },
    {
      $group: {
        _id: "$_id",
        name: { $first: "$name" },
        LDName: { $first: "$LDName" },
        jamiat: { $first: "$jamiat" },
        jamaat: { $first: "$jamaat" },
        logo: { $first: "$logo" },
        phone: { $first: "$phone" },
        whatsapp: { $first: "$whatsapp" },
        ITSID: { $first: "$ITSID" },
        gender: { $first: "$gender" },
        maritialStatus: { $first: "$maritialStatus" },
        occupation: { $first: "$occupation" },
        qualification: { $first: "$qualification" },
        organization: { $first: "$organization" },
        prefix: { $first: "$prefix" },
        misaq: { $first: "$misaq" },
        occupation: { $first: "$occupation" },
        qualification: { $first: "$qualification" },
        idara: { $first: "$idara" },
        category: { $first: "$category" },
        organization: { $first: "$organization" },
        address: { $first: "$address" },
        city: { $first: "$city" },
        country: { $first: "$country" },
        nationality: { $first: "$nationality" },
        vatan: { $first: "$vatan" },
        email: { $first: "$email" },
        miqaats: { $push: "$miqaats" },
        systemRole: { $first: "$systemRole" },
      },
    },
    {
      $project: {
        _id: 1,
        logo: 1,
        phone: 1,
        whatsapp: 1,
        name: 1,
        LDName: 1,
        ITSID: 1,
        email: 1,
        status: 1,
        gender: 1,
        maritialStatus: 1,
        occupation: 1,
        qualification: 1,
        organization: 1,
        prefix: 1,
        misaq: 1,
        occupation: 1,
        qualification: 1,
        idara: 1,
        category: 1,
        organization: 1,
        address: 1,
        city: 1,
        country: 1,
        nationality: 1,
        vatan: 1,
        createdAt: 1,
        updatedAt: 1,
        jamiat: { id: "$jamiat._id", name: "$jamiat.name" },
        jamaat: { id: "$jamaat._id", name: "$jamaat.name" },
        systemRole: { id: "$systemRole._id", name: "$systemRole.name" },
        "miqaats.isActive": 1,
        "miqaats.consentRequired": 1,
        "miqaats.consentAccepted": 1,
        "miqaats.isInternationalPlugin": 1,
        "miqaats._id": 1,
        "miqaats.miqaat": 1,
        "miqaats.arazCity": 1,
        "miqaats.kgType": 1,
        "miqaats.kgGroup": 1,
        "miqaats.hierarchyPosition": 1,
        "miqaats.zone": 1,
        "miqaats.department": 1,
        "miqaats.razaStatus": 1,
        "miqaats.function": 1,
      },
    },
  ]);
  if (isEmpty(userData[0])) {
    return apiError(NOT_FOUND, "User", null, res);
  }

  userData = userData[0];
  if (userData?.systemRole?.name) {
    userData.position = `${userData?.systemRole?.name}`;
  }
  userData.miqaats = await Promise.all(
    userData.miqaats.filter((miqaat) => miqaat.isActive && miqaat.miqaat.status === "active" && miqaat.arazCity.status === true).map(async (miqaat) => {
      let position = "User";

      if (miqaat?.hierarchyPosition?.name) {
        position = miqaat?.arazCity?.showPositionAlias
          ? miqaat?.hierarchyPosition?.alias
          : miqaat?.hierarchyPosition?.name;
      }

      if (miqaat?.zone?.name) {
        position = `${miqaat?.zone?.name} ${position}`;
      }

      if (miqaat?.department?.name) {
        position = `${miqaat?.department?.name} ${position}`;
      }

      miqaat.headDetails = await getHeadDetails(miqaat, req.allowPhone);
      miqaat.position = position;

      return miqaat;
    })
  );
  await setCache(cacheKey, userData);
  return apiResponse(FETCH, "User Profile", userData, res);
});

const getHeadDetails = async (userMiqaat, allowPhone) => {
  try {
    const { arazCity, zone, department, hierarchyPosition } = userMiqaat;

    const currentPositionId = hierarchyPosition?.id?.toString();

    if (
      currentPositionId !==
      constants.HIERARCHY_POSITIONS.ZONE_TEAM[0].toString()
    )
      return [];
    const pipeline = [
      { $unwind: "$miqaats" },
      {
        $match: {
          "miqaats.hierarchyPositionID":
            constants.HIERARCHY_POSITIONS.ZONE_LEAD[0],
          "miqaats.isActive": true,
          "miqaats.arazCityID": toObjectId(arazCity.id),
          "miqaats.arazCityZoneID": toObjectId(zone.id),
          "miqaats.departmentID": toObjectId(department.id),
        },
      },
      {
        $lookup: {
          from: "hierarchypositions",
          localField: "miqaats.hierarchyPositionID",
          foreignField: "_id",
          as: "hierarchyPosition",
        },
      },
      { $unwind: "$hierarchyPosition" },
      {
        $lookup: {
          from: "arazcities",
          localField: "miqaats.arazCityID",
          foreignField: "_id",
          as: "arazCity",
        },
      },
      { $unwind: { path: "$arazCity", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "arazcityzones",
          localField: "miqaats.arazCityZoneID",
          foreignField: "_id",
          as: "zone",
        },
      },
      { $unwind: { path: "$zone", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "departments",
          localField: "miqaats.departmentID",
          foreignField: "_id",
          as: "department",
        },
      },
      { $unwind: { path: "$department", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "kgtypes",
          localField: "miqaats.kgTypeID",
          foreignField: "_id",
          as: "kgType",
        },
      },
      { $unwind: { path: "$kgType", preserveNullAndEmptyArrays: true } },
      { $sort: { "kgType.priority": 1 } },
      {
        $project: {
          _id: 1,
          name: 1,
          phone: 1,
          whatsapp: 1,
          email: 1,
          logo: 1,
          gender: 1,
          ITSID: 1,
          position: {
            id: "$hierarchyPosition._id",
            name: "$hierarchyPosition.name",
            alias: "$hierarchyPosition.alias",
          },
          KGType: {
            id: "$kgType._id",
            name: "$kgType.name",
            priority: "$kgType.priority",
          },
          arazCity: {
            id: "$arazCity._id",
            name: "$arazCity.name",
            showPositionAlias: "$arazCity.showPositionAlias",
          },
          zone: {
            id: "$zone._id",
            name: "$zone.name",
          },
          department: {
            id: "$department._id",
            name: "$department.name",
          },
          miqaatId: "$miqaats._id",
        },
      },
    ];
    const headData = await KGUser.aggregate(pipeline);
    return headData.map((head) => {
      let positionString = "User";
      if (head.position?.name) {
        positionString = head.arazCity?.showPositionAlias
          ? head.position.alias || head.position.name
          : head.position.name;
      }
      if (head.zone?.name) {
        positionString = `${head.zone.name} ${positionString}`;
      }
      if (head.department?.name) {
        positionString = `${head.department.name} ${positionString}`;
      }

      return {
        id: head._id,
        name: head.name,
        email: head.email,
        ITSID: head.ITSID,
        position: positionString,
        hierarchyPosition: head.position,
        KGType: head.KGType,
        arazCity: head.arazCity,
        zone: head.zone,
        department: head.department,
        miqaatId: head.miqaatId,
        ...(head.gender === "M"
          ? { phone: head.phone, whatsapp: head.whatsapp, logo: head.logo }
          : allowPhone
            ? { phone: head.phone, whatsapp: head.whatsapp, logo: head.logo }
            : {}),
      };
    });
  } catch (error) {
    console.error("Error fetching head details:", error);
    return [];
  }
};



module.exports = {
  loginUserV3,
  getUserProfile,

};
