import moment from 'moment-timezone';
/**
 * Converts UTC time to specified timezone using moment-timezone
 * @param {string|Date|moment} utcTime - UTC time (ISO string, Date object, or moment object)
 * @param {Object} timeZoneConfig - Timezone configuration object
 * @param {string} [outputFormat='YYYY-MM-DD HH:mm:ss'] - Output format for the converted time
 * @param {boolean} [returnMomentObject=false] - Return moment object instead of formatted string
 * @returns {string|moment|null} Converted time in specified timezone or null if invalid
 */
export const convertUTCToTimezone = (
  utcTime,
  timeZoneConfig,
  outputFormat = 'YYYY-MM-DD hh:mm A',
  includeTime = false,
  returnMomentObject = false
) => {
  try {
    // Validate inputs
    if (!utcTime || !timeZoneConfig) {
      console.error('UTC time and timezone configuration are required');
      return null;
    }

    const timezone = timeZoneConfig.value;
    if (!timezone) {
      console.error('Timezone value is missing in configuration');
      return null;
    }

    // Create moment object from UTC time
    let utcMoment;
    if (moment.isMoment(utcTime)) {
      utcMoment = utcTime.clone().utc();
    } else {
      utcMoment = moment.utc(utcTime);
    }

    if (!utcMoment.isValid()) {
      console.error('Invalid UTC time provided');
      return null;
    }

    // Convert to specified timezone
    const convertedTime = utcMoment.tz(timezone);

    // Handle includeTime logic
    let format = outputFormat;
    if (typeof includeTime !== 'undefined') {
      format = includeTime ? 'DD-MM-YYYY hh:mm A' : 'DD-MM-YYYY';
    }

    if (returnMomentObject) {
      return convertedTime;
    }

    return convertedTime.format(format);
  } catch (error) {
    console.error('Error converting UTC to timezone:', error);
    return null;
  }
};

export const convertUTCDateAndTimeToTimezone = (utcDate, timeObj, timeZoneConfig, outputFormat = 'MMM D, YYYY, hh:mm A') => {
  if (!utcDate || !timeObj || !timeZoneConfig) return '';
  // Combine date and time into a single UTC datetime string
  const hh = String(timeObj.hh || '').padStart(2, '0');
  const mm = String(timeObj.mm || '').padStart(2, '0');
  const period = timeObj.period || 'AM';
  // Convert to 24-hour format
  let hour = parseInt(hh, 10);
  if (period === 'PM' && hour < 12) hour += 12;
  if (period === 'AM' && hour === 12) hour = 0;
  const combinedUTC = moment.utc(utcDate).set({ hour, minute: parseInt(mm, 10), second: 0 });
  return combinedUTC.tz(timeZoneConfig.value).format(outputFormat);
};

// Usage remains the same as the previous examples
export const convertBatchUTCToTimezone = (utcTimes, timeZoneConfig, outputFormat = 'YYYY-MM-DD hh:mm A') => {
  if (!Array.isArray(utcTimes)) {
    console.error('utcTimes must be an array');
    return [];
  }

  return utcTimes.map((utcTime) => convertUTCToTimezone(utcTime, timeZoneConfig, outputFormat)).filter((time) => time !== null);
};

export const getCurrentTimeInTimezone = (timeZoneConfig, outputFormat = 'YYYY-MM-DD hh:mm A', returnMomentObject = false) => {
  try {
    const timezone = timeZoneConfig.value;
    if (!timezone) {
      console.error('Timezone value is missing in configuration');
      return null;
    }

    const currentTime = moment().tz(timezone);

    if (returnMomentObject) {
      return currentTime;
    }

    return currentTime.format(outputFormat);
  } catch (error) {
    console.error('Error getting current time in timezone:', error);
    return null;
  }
};

/**
 * Get timezone offset information
 * @param {Object} timeZoneConfig - Timezone configuration object
 * @returns {Object|null} Timezone information including offset, abbreviation, etc.
 */
export const getTimezoneInfo = (timeZoneConfig) => {
  try {
    const timezone = timeZoneConfig.value;
    if (!timezone) {
      console.error('Timezone value is missing in configuration');
      return null;
    }

    const now = moment().tz(timezone);

    return {
      timezone: timezone,
      label: timeZoneConfig.label,
      offset: now.format('Z'),
      offsetMinutes: now.utcOffset(),
      abbreviation: now.format('z'),
      isDST: now.isDST()
    };
  } catch (error) {
    console.error('Error getting timezone info:', error);
    return null;
  }
};

// Usage Examples:

// Example 1: Basic conversion
const timeZoneConfig = {
  label: 'Houston (America/Chicago)',
  value: 'America/Chicago',
  _id: '6879e6c00b9290f1fda20677'
};

const utcTime = '2024-01-15T10:30:00Z';
const convertedTime = convertUTCToTimezone(utcTime, timeZoneConfig);
// console.log('Converted time:', convertedTime);

// Example 2: Custom format
const customFormat = convertUTCToTimezone(utcTime, timeZoneConfig, 'MMM DD, YYYY hh:mm A');
// console.log('Custom format:', customFormat);

// Example 3: Get moment object for further manipulation
const momentObj = convertUTCToTimezone(utcTime, timeZoneConfig, null, true);
// console.log('Moment object:', momentObj);

// Example 4: Batch conversion
const utcTimes = ['2024-01-15T10:30:00Z', '2024-01-15T14:45:00Z', '2024-01-15T18:00:00Z'];
const batchConverted = convertBatchUTCToTimezone(utcTimes, timeZoneConfig);
// console.log('Batch converted:', batchConverted);

// Example 5: Get current time in timezone
const currentTime = getCurrentTimeInTimezone(timeZoneConfig);
// console.log('Current time in timezone:', currentTime);

// Example 6: Get timezone information
const timezoneInfo = getTimezoneInfo(timeZoneConfig);
// console.log('Timezone info:', timezoneInfo);

const userTimezone = {
  label: 'Houston (America/Chicago)',
  value: 'America/Chicago',
  _id: '6879e6c00b9290f1fda20677'
};

// Convert UTC timestamps from your database
const dbTimestamp = '2024-01-15T10:30:00Z';
const localTime = convertUTCToTimezone(dbTimestamp, userTimezone);
// Output: "2024-01-15 16:00:00"

// For display in React components
const displayTime = convertUTCToTimezone(dbTimestamp, userTimezone, 'MMM DD, YYYY hh:mm A');
// Output: "Jan 15, 2024 04:00 PM"

// Batch convert for lists/tables
const timestamps = ['2024-01-15T10:30:00Z', '2024-01-15T14:45:00Z'];
const convertedTimes = convertBatchUTCToTimezone(timestamps, userTimezone);

//   console.log(localTime, 'localTime');
//   console.log(displayTime, 'displayTime');
//   console.log(convertedTimes, 'convertedTimes');
