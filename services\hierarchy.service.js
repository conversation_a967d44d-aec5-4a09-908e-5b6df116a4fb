const express = require("express");
const cors = require("cors");
const path = require("path");
const compression = require("compression");
const { CORS_ORIGIN, NODE_ENV, HIERARCHY_PORT } = require("../constants");
const routes = require("../modules/hierarchy/routes");
const { authGuard, roleGuard } = require("../middlewares/guard.middleware");
const { apiLoggerMiddleware, apiErrorLoggerMiddleware, attachStartTime } = require("../middlewares/apiLogger.middleware");
const { addServiceHeader } = require("../middlewares/serviceHeader.middleware");
const { connectDB } = require("../db");

const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));

// Add service header middleware
app.use(addServiceHeader('hierarchy'));

// Add API logging middleware
app.use(attachStartTime);
app.use(apiLoggerMiddleware);

app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});


// Hierarchy routes
app.use("/api", authGuard, roleGuard, routes);

// Legacy HR routes (will be removed in future)
const hrRoutes = require("../modules/hierarchy/routes/interest.route");
app.use("/api/hr/interest", authGuard, roleGuard, hrRoutes);

app.get("/", (req, res) => {
  res.send(`Hierarchy Service is running on port: ${HIERARCHY_PORT}`);
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({ 
    status: "healthy", 
    service: "hierarchy",
    port: HIERARCHY_PORT,
    timestamp: new Date().toISOString()
  });
});

// Add error logging middleware at the end
app.use(apiErrorLoggerMiddleware);

// Start server
const startServer = async () => {
  try {
    await connectDB();
    app.listen(HIERARCHY_PORT, () => {
      console.log(`Node environment: ${NODE_ENV}`);
      console.log(`Hierarchy Service running on port: ${HIERARCHY_PORT}`);
    });
  } catch (err) {
    console.log(`DB Error: ${err.message}`);
    process.exit(1);
  }
};

if (require.main === module) {
  startServer();
}

module.exports = app;
