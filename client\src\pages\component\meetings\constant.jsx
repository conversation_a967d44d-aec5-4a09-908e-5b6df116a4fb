import { ID_8_DIGITS_REGEX } from 'utils/constant';
import { generateUniqueId, getOptions, notification } from 'utils/helper';
import * as Yup from 'yup';
import { cloneDeep, get, isEmpty } from 'lodash';
import { getActiveDepartmentByArazCityAction } from 'redux/actions/departmentsAction';
import { getZoneyByArazCityAction } from 'redux/actions/zoneAction';
import {
  ALL_DEPARTMENT_HOD,
  ALL_IN_DEPARTMENT,
  ALL_IN_HIERARCHY,
  ALL_IN_ZONE,
  ALL_ZONE_HOD,
  BY_ITS,
  CUSTOM,
  recipentFields
} from '../compose/constant';
import moment from 'moment';
import { convertUTCDateAndTimeToTimezone, convertUTCToTimezone } from 'utils/timeZoneHelper';

export const dummyMeetingList = [
  {
    _id: generateUniqueId(),
    name: 'HR Tasks Discussion',
    date: '2025-05-19T12:00'
  },
  {
    _id: generateUniqueId(),
    name: 'Acknowledgement for Khidmat',
    date: '2025-04-13T17:09'
  },
  {
    _id: generateUniqueId(),
    name: 'ITS scanning',
    date: '2024-05-09T12:42'
  },
  {
    _id: generateUniqueId(),
    name: 'CMZ Khidmat IT',
    date: '2024-05-13T17:53'
  },
  {
    _id: generateUniqueId(),
    name: 'Khidmat Acknowledgement for IT',
    date: '2025-05-30T14:16'
  },
  {
    _id: generateUniqueId(),
    name: 'HR Meeting',
    date: '2024-06-24T12:48'
  },
  {
    _id: generateUniqueId(),
    name: 'Ashara 1446 Registration for Nazafat Khidmat',
    date: '2024-06-02T17:14'
  }
];

export const dummyActionableList = [
  {
    logo: '',
    ITSID: '30351956',
    name: 'Husain Bhai Bandook Wala',
    department: 'Mawaid',
    zone: 'CMZ',
    position: 'Zone Head',
    tasks: 'Volunteer Attendance & Check-In'
  },
  {
    logo: '',
    ITSID: '30403910',
    name: 'Husain Bhai Binding Wala',
    department: 'HR',
    zone: 'CMZ',
    position: 'Zone Team',
    tasks: 'Issue Handling'
  },
  {
    logo: '',
    ITSID: '30352107',
    name: 'Mohammed Bhai Mahershwar Wala',
    department: 'Finance',
    zone: 'CMZ',
    position: 'Zone Team',
    tasks: 'Volunteer Coordination'
  },
  {
    logo: '',
    ITSID: '30704078',
    name: 'Taha Bhai Binding Wala',
    department: 'Nazafat',
    zone: 'CMZ',
    position: 'Zone Team',
    tasks: 'Issue Handling'
  },
  {
    logo: '',
    ITSID: '30488344',
    name: 'Taha Bhai Manaquib Wala',
    department: 'Mawaid',
    zone: 'CMZ',
    position: 'Zone Lead',
    tasks: 'Emergency Management'
  },
  {
    logo: '',
    ITSID: '30344951',
    name: 'Aliasgar Bhai Sadiqal Bhai Hotelwala',
    department: 'IT',
    zone: 'CMZ',
    position: 'Zone Lead',
    tasks: 'Emergency Management'
  },
  {
    logo: '',
    ITSID: '30347633',
    name: 'Shabbir Bhai Tajpur Wala',
    department: 'IT',
    zone: 'CMZ',
    position: 'Zone Team',
    tasks: 'Volunteer Coordination'
  }
];

export const meetingValidationSchema = Yup.object().shape({
  name: Yup.string().trim().required('Meeting Title is required')
  // hostedBy: Yup.string().trim().required('Hosted By is required'),
  // meetingLink: Yup.string()
  //   .trim()
  //   .url('Enter a valid URL')
  //   .test('link-or-address', 'Meeting Link is required if Address is empty', function (value) {
  //     const { meetingAddress } = this.parent;
  //     if (!value && !meetingAddress?.trim()) {
  //       return this.createError({ message: 'Meeting Link is required if Address is empty' });
  //     }
  //     return true;
  //   }),

  // meetingAddress: Yup.string()
  //   .trim()
  //   .test('address-or-link', 'Meeting Address is required if Link is empty', function (value) {
  //     const { meetingLink } = this.parent;
  //     if (!value && !meetingLink?.trim()) {
  //       return this.createError({ message: 'Meeting Address is required if Link is empty' });
  //     }
  //     return true;
  //   }),
  // date: Yup.date()
  //   .typeError('Please enter a valid date')
  //   .min(new Date(new Date().setHours(0, 0, 0, 0)), 'Date cannot be in the past')
  //   .required('Date is required'),
  //   time: Yup.string()
  //   .trim()
  //   .required('Time is required')
  //   .test('is-future-time', 'Time must be in the future', function (value) {
  //     const { date } = this.parent;

  //     if (!value || !date) {
  //       return this.createError({ message: 'Both Date and Time are required' });
  //     }

  //     const now = new Date();

  //     // Parse the selected date
  //     const selectedDate = new Date(date);
  //     console.log(selectedDate,'selectedDate',value)
  //     if (isNaN(selectedDate.getTime())) {
  //       return this.createError({ message: 'Invalid date' });
  //     }

  //     // Parse time string (format: HH:mm)
  //     const [hours, minutes] = value.split(':').map(Number);
  //     if (
  //       isNaN(hours) ||
  //       isNaN(minutes) ||
  //       hours < 0 ||
  //       hours > 23 ||
  //       minutes < 0 ||
  //       minutes > 59
  //     ) {
  //       return this.createError({ message: 'Invalid time format' });
  //     }

  //     const selectedDateTime = new Date(
  //       selectedDate.getFullYear(),
  //       selectedDate.getMonth(),
  //       selectedDate.getDate(),
  //       hours,
  //       minutes,
  //       0
  //     );

  //     if (selectedDateTime <= now) {
  //       return this.createError({ message: 'Selected time must be in the future' });
  //     }

  //     return true;
  //   }),
});

export const formatToHHMM = (input) => {
  const date = new Date(input);
  if (isNaN(date.getTime())) return ''; // return empty string if invalid

  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${hours}:${minutes}`;
};

const stripHtml = (html) => {
  const div = document.createElement('div');
  div.innerHTML = html;
  return div.textContent || div.innerText || '';
};

export const agendaValidationSchema = Yup.object().shape({
  agenda: Yup.string()
    .required('Agenda is required')
    .test('is-not-empty-html', 'Agenda is required', (value) => {
      return stripHtml(value || '').trim().length > 0;
    }),

  actionable: Yup.string()
    .required('Actionable is required')
    .test('is-not-empty-html', 'Actionable is required', (value) => {
      return stripHtml(value || '').trim().length > 0;
    })
});

export const validateAgenda = (data, dispatch) => {
  if (!data?.agenda?.trim()) {
    dispatch(notification(false, 'Agenda is required', true));
    return false;
  }

  return true;
};

export const updatedRecipientField = (fields, val) => {
  return fields?.map((field) => {
    if ((val === ALL_IN_DEPARTMENT || val === ALL_DEPARTMENT_HOD) && field?.name === 'departmentIDs') {
      return { ...field, hideField: false };
    }
    if ((val === ALL_IN_ZONE || val === ALL_ZONE_HOD) && field?.name === 'zoneIDs') {
      return { ...field, hideField: false };
    }
    if (val === BY_ITS && field?.name === 'ITSIDs') {
      return { ...field, hideField: false };
    }
    if (val === CUSTOM && ['positionIDs', 'zoneIDs', 'departmentIDs', 'kgTypeIDs', 'kgGroupIDs'].includes(field?.name)) {
      return { ...field, hideField: false };
    }
    return { ...field, hideField: true };
  });
};

export const updatedOptions = (fields, departmentData, zoneData, kgData, arazCityData, val) => {
  let { allKGType, allKGGroup, allHierarchyPosition } = kgData || {};
  let singleArazCity = arazCityData?.arazCityForSingleMiqaat?.find((arazCity) => arazCity?._id === val);

  return fields
    ?.map((field) => {
      if (field?.name === 'departmentIDs') {
        return {
          ...field,
          items: getOptions(get(departmentData, 'payload.data', []), 'name', 'id')
        };
      }
      if (field?.name === 'zoneIDs') {
        return {
          ...field,
          items: getOptions(get(zoneData, 'payload.data', []), 'name', '_id')
        };
      }
      if (field?.name === 'kgTypeIDs') {
        return {
          ...field,
          items: getOptions(allKGType, 'name', '_id')
        };
      }
      if (field?.name === 'kgGroupIDs') {
        return {
          ...field,
          items: getOptions(allKGGroup, 'name', '_id')
        };
      }
      if (field?.name === 'positionIDs') {
        return {
          ...field,
          items: getOptions(allHierarchyPosition, singleArazCity?.showPositionAlias ? 'alias' : 'name', '_id')
        };
      }
      return field;
    })
    ?.filter(Boolean);
};

export const validateInvitees = (invitees = [], value, dispatch) => {
  const isEmptyArray = (arr) => !Array.isArray(arr) || arr.length === 0;

  const hasSystemPermission = !isEmpty(value?.systemPermissionID);
  const hasInvitees = Array.isArray(invitees) && invitees.length === 1 && invitees?.[0]?.arazCityID;

  if (!hasSystemPermission && !hasInvitees) {
    dispatch(notification(false, 'Either System Permission or at least one invitee must be selected.', true));
    return false;
  }

  if (!hasSystemPermission) {
    for (let index = 0; index < invitees.length; index++) {
      const { arazCityID, memberType, fieldValues = {} } = invitees[index];

      if (!arazCityID) {
        dispatch(notification(false, `Invitee ${index + 1}: Araz City is required`, true));
        return false;
      }

      switch (memberType) {
        case ALL_IN_HIERARCHY:
          return true;
        case 'ALL_IN_DEPARTMENT':
        case 'ALL_DEPARTMENT_HOD':
          if (isEmptyArray(fieldValues.departmentIDs)) {
            dispatch(notification(false, `Invitee ${index + 1}: Department(s) required for ${memberType}`, true));
            return false;
          }
          break;

        case 'ALL_IN_ZONE':
        case 'ALL_ZONE_HOD':
          if (isEmptyArray(fieldValues.zoneIDs)) {
            dispatch(notification(false, `Invitee ${index + 1}: Zone(s) required for ${memberType}`, true));
            return false;
          }
          break;

        case 'BY_ITS':
          const itsIDs = fieldValues?.ITSIDs?.split('\n')?.map((id) => id?.trim());
          const invalidITSIDs = itsIDs?.filter((id) => !ID_8_DIGITS_REGEX.test(id));
          if (isEmptyArray(itsIDs) || invalidITSIDs?.length) {
            dispatch(notification(false, `Invitee ${index + 1}: ITSIDs are required`, true));
            return false;
          }
          break;

        case 'CUSTOM':
          const hasAnyCustomField =
            !isEmptyArray(fieldValues.departmentIDs) ||
            !isEmptyArray(fieldValues.zoneIDs) ||
            !isEmptyArray(fieldValues.positionIDs) ||
            !isEmptyArray(fieldValues.kgGroupIDs) ||
            !isEmptyArray(fieldValues.kgTypeIDs);

          if (!hasAnyCustomField) {
            dispatch(
              notification(
                false,
                `Invitee ${index + 1}: At least one of Department, Zone, Position, KG Group, or KG Type is required for CUSTOM`,
                true
              )
            );
            return false;
          }
          break;

        default:
          dispatch(notification(false, `Invitee ${index + 1}: Invalid or missing recipient type`, true));
          return false;
      }
    }
  }

  return true;
};

export function isMeetingInPast(date, time = null) {
  if (!date) return false;

  const baseDate = new Date(date);
  if (isNaN(baseDate.getTime())) return false;

  if (time) {
    let hour = parseInt(time.hh || '0', 10);
    const minute = parseInt(time.mm || '0', 10);
    const period = time.period || 'AM';

    if (period === 'PM' && hour !== 12) hour += 12;
    if (period === 'AM' && hour === 12) hour = 0;

    baseDate.setHours(hour);
    baseDate.setMinutes(minute);
    baseDate.setSeconds(0);
    baseDate.setMilliseconds(0);
  } else {
    baseDate.setHours(23, 59, 59, 999);
  }

  return baseDate < new Date();
}

export const formatMeetingData = (meetings) => {
  const timeZoneConfig = JSON.parse(localStorage.getItem('selectedArazCityTimeZone'));
  return meetings?.map((item) => {
    const date = get(item, 'date');
    const time = get(item, 'time');

    // Format date and time together in the required format and timezone
    const displayDateTime =
      date && time && timeZoneConfig
        ? convertUTCDateAndTimeToTimezone(date, time, timeZoneConfig, 'MMMM DD, YYYY , hh:mm A')
        : date && timeZoneConfig
          ? convertUTCToTimezone(date, timeZoneConfig, 'MMMM DD, YYYY')
          : '';

    return {
      ...item,
      name: item.title,
      date: displayDateTime,
      createdBy:
        item?.meetingCreatedBy?.ITSID && item?.meetingCreatedBy?.name
          ? `${item?.meetingCreatedBy?.ITSID} - ${item?.meetingCreatedBy?.name}`
          : item?.meetingCreatedBy?.ITSID || item?.meetingCreatedBy?.name,
      meetingDone: isMeetingInPast(date, time)
    };
  });
};
