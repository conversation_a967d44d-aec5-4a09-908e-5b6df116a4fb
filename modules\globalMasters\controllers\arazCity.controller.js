const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON><PERSON>r,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");

const {
  ArazCity,
  Department,
  ArazCityZone,
  Hierarchy,
  KGUser,
  Jamaat,
  HierarchyPosition,
  Miqaat,
} = require("../../hierarchy/models");

const path = require("path");
const {
  isEmpty,
  toObjectId,
  getUniqueName,
  sortByPriorityAndName,
} = require("../../../utils/misc.util");
const {
  generateHierarchy,
} = require("../../hierarchy/controllers/hierarchy.controller");
const { error } = require("console");
const constants = require("../../../constants");
const { ZoneMapping } = require("../../zonesCapacity/models");
const {
  redisCacheKeys,
  setCache,
  getCache,
} = require("../../../utils/redis.cache");
const {
  addEventLog,
  EventActions,
  Modules,
} = require("../../../utils/eventLogs.util");
const {
  isActiveMiqaatAndArazCity,
} = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const { TimeZone } = require("../models/timeZone.model");

const getAllArazCities = apiHandler(async (req, res) => {
  let QueryObject = {};
  if (!req.query.status) {
    QueryObject.status = true;
  }

  if (isEmpty(req.user.systemRoleID)) {
    const arazCityIDs = req.user.miqaats.map((miqaat) => miqaat.arazCityID);
    QueryObject._id = { $in: arazCityIDs };
  }

  const arazCities = await ArazCity.find(
    QueryObject,
    "_id name miqaatID logo fasalDate status"
  )
    .populate("miqaatID", "_id name")
    .populate("timeZoneID", "label value")
    .sort({ _id: -1 });

  return apiResponse(FETCH, "Araz Cities", arazCities, res);
});

const addArazCity = apiHandler(async (req, res) => {
  const { name, miqaatID, jamiats, jamaats, fasalDate, isFasal } = req.body;

  const uniqueName = getUniqueName(name);

  await clearCacheByPattern(`*`, miqaatID);

  const existingArazCity = await ArazCity.findOne({ uniqueName, miqaatID });
  if (!isEmpty(existingArazCity)) {
    return apiError(
      CUSTOM_ERROR,
      "Araz City with this name already exists",
      null,
      res
    );
  }

  const miqaat = await Miqaat.findById(miqaatID).select("name");

  const departments = await Department.aggregate([
    { $sort: { _id: -1 } },
    {
      $project: {
        departmentID: "$_id",
        name: 1,
        status: 1,
        hierarchyPositions: 1,
        isZonal: 1,
      },
    },
  ]);

  const hierarchyPositions = await HierarchyPosition.aggregate([
    { $sort: { _id: -1 } },
    {
      $project: {
        hierarchyPositionID: "$_id",
        name: 1,
        countRecommendation: 1,
        weightage: 1,
      },
    },
  ]);

  const defaultArazCityZone = await ArazCityZone.findOne(
    {
      uniqueName: "cmz",
    },
    "_id name"
  );

  let arazCityData = {
    ...req.body,
    name,
    uniqueName,
    miqaatID,
    fasalDate,
    isFasal,
    jamiats,
    jamaats,
    departments,
    hierarchyPositions,
    arazCityZones: [defaultArazCityZone._id],
    createdBy: req.user._id,
  };

  const City_Logo_Path = req?.file?.path;

  if (!isEmpty(City_Logo_Path)) {
    const relativePath = path
      .relative(process.cwd(), City_Logo_Path)
      .replace(/\\+/g, "/");

    arazCityData.logo = relativePath;
  }

  const newArazCity = new ArazCity(arazCityData);

  const arazCity = await newArazCity.save();

  if (!isEmpty(fasalDate)) {
    await ArazCity.updateMany(
      { miqaatID, _id: { $ne: arazCity._id } },
      { status: false }
    );
  }

  await addEventLog(
    EventActions.CREATE,
    Modules.GlobalMasters,
    "Araz City",
    req.user._id
  );

  apiResponse(ADD_SUCCESS, "Araz City", arazCity, res);

  await generateHierarchy(miqaatID, newArazCity._id);

  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Hierarchy for Araz City",
    constants.AMS_SYSTEMID
  );
});

const getSingleArazCity = apiHandler(async (req, res) => {
  const { id } = req.params;

  const arazCityData = await ArazCity.findOne({
    _id: id,
    // status: true
  })
    .populate("miqaatID")
    .populate("timeZoneID", "label value")
    .populate({
      path: "departments.departmentID",
      select: "allowDeactivate name isZonal",
    })
    .lean();

  if (arazCityData && arazCityData.departments) {
    arazCityData.departments = arazCityData.departments.map((dept) => {
      const { departmentID, ...rest } = dept;
      return {
        ...rest,
        ...(departmentID || {}),
      };
    });
  }

  if (isEmpty(arazCityData)) {
    return apiError(NOT_FOUND, "Araz City", null, res);
  }

  return apiResponse(FETCH, "Araz City", arazCityData, res);
});

const editArazCity = apiHandler(async (req, res) => {
  const {
    id,
    name,
    LDName,
    miqaatID,
    jamiats,
    jamaats,
    fasalDate,
    isFasal,
    showPositionAlias,
    timeZoneID,
    status
  } = req.body;

  const uniqueName = getUniqueName(name);

  await clearCacheByPattern(`*`, miqaatID, id);

  const existingArazCity = await ArazCity.findOne({
    _id: { $ne: id },
    miqaatID,
    uniqueName,
  });

  if (!isEmpty(existingArazCity)) {
    return apiError(
      CUSTOM_ERROR,
      "Araz City with this name already exists",
      null,
      res
    );
  }

  let updateData = {
    name,
    LDName,
    miqaatID,
    fasalDate,
    isFasal,
    jamiats,
    jamaats,
    showPositionAlias,
    updatedBy: req.user._id,
    timeZoneID,
    status
  };

  const City_Logo_Path = req?.file?.path;

  if (!isEmpty(City_Logo_Path)) {
    const relativePath = path
      .relative(process.cwd(), City_Logo_Path)
      .replace(/\\+/g, "/");
    updateData.logo = relativePath;
  }

  const arazCityData = await ArazCity.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });

  if (isEmpty(arazCityData)) {
    return apiError(NOT_FOUND, "Araz City", null, res);
  }

  if (!isEmpty(fasalDate)) {
    await ArazCity.updateMany(
      { miqaatID, _id: { $ne: toObjectId(arazCityData._id) } },
      { status: false }
    );
  }

  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Araz City",
    req.user._id
  );

  apiResponse(UPDATE_SUCCESS, "Araz City", arazCityData, res);

  await generateHierarchy(miqaatID, arazCityData._id);

  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Hierarchy for Araz City",
    constants.AMS_SYSTEMID
  );
});

const deleteArazCity = apiHandler(async (req, res) => {
  const { id } = req.params;

  await clearCacheByPattern(`*`, null, id);

  const existsInArazCityZone = await ArazCityZone.exists({ arazCityID: id });

  if (existsInArazCityZone) {
    return apiError(
      CUSTOM_ERROR,
      "Couldn't Delete zone as present in an ArazCity",
      null,
      res
    );
  }

  const arazCityData = await ArazCity.findOneAndUpdate(
    { _id: toObjectId(id) },
    { $set: { status: false } }
  );

  if (isEmpty(arazCityData)) {
    return apiError(NOT_FOUND, "Araz City", null, res);
  }

  await addEventLog(
    EventActions.DELETE,
    Modules.GlobalMasters,
    "Araz City",
    req.user._id
  );

  apiResponse(DELETE_SUCCESS, "Araz City", null, res);

  await Hierarchy.deleteMany({ arazCityID: toObjectId(id) });

  await addEventLog(
    EventActions.DELETE,
    Modules.GlobalMasters,
    "Hierarchy for Araz City",
    constants.AMS_SYSTEMID
  );
});

const getArazCityByMiqaat = apiHandler(async (req, res) => {
  const { id } = req.params;
  let QueryObject = {};
  // QueryObject.status = true;
  QueryObject.miqaatID = id;

  const cacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.ARAZ_CITIES}:${id}`;

  let data = await getCache(cacheKey, id);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Araz Cities", data, res, true);
  }

  let arazCityData = await ArazCity.find(QueryObject)
    .populate({
      path: "miqaatID",
      select: "_id name",
    })
    .populate({
      path: "timeZoneID",
      select: "_id label value",
    })
    .select("_id name miqaatID jamiats jamaats showPositionAlias ");

  if (isEmpty(arazCityData)) {
    return apiError(NOT_FOUND, "Araz Cities", null, res);
  }

  setCache(cacheKey, arazCityData, id);

  return apiResponse(FETCH, "Araz Cities", arazCityData, res);
});

const getArazCityReport = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;
  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const cmzZoneID = constants.ARAZ_CITY_ZONES.CMZ[0];

  const arazCityZones = await ArazCityZone.find({
    $or: [{ arazCityID }, { _id: cmzZoneID }],
  })
    .lean()
    .sort({ createdAt: -1 });

  const zonal = arazCityZones.map((zone) => ({
    zoneId: zone._id,
    zoneName: zone.name,
    mardoCount: 0,
    bairaoCount: 0,
    gairBalighDikraoCount: 0,
    gairBalighDikrioCount: 0,
    total: 0,
  }));

  const zoneMappings = await ZoneMapping.find({
    arazCityZoneID: { $in: arazCityZones.map((zone) => zone._id) },
    miqaatID,
  }).lean();

  zoneMappings.forEach((mapping) => {
    const zoneData = zonal.find(
      (z) => z.zoneId.toString() === mapping.arazCityZoneID.toString()
    );
    if (zoneData) {
      zoneData.mardoCount += mapping.gents || 0;
      zoneData.bairaoCount += mapping.ladies || 0;
      zoneData.gairBalighDikraoCount += mapping.childrenDikrao || 0;
      zoneData.gairBalighDikrioCount += mapping.childrenDikrio || 0;
      zoneData.total =
        zoneData.mardoCount +
        zoneData.bairaoCount +
        zoneData.gairBalighDikraoCount +
        zoneData.gairBalighDikrioCount;
    }
  });

  const totalCounts = {
    zoneName: "Total",
    mardoCount: zonal.reduce((sum, zone) => sum + zone.mardoCount, 0),
    bairaoCount: zonal.reduce((sum, zone) => sum + zone.bairaoCount, 0),
    gairBalighDikraoCount: zonal.reduce(
      (sum, zone) => sum + zone.gairBalighDikraoCount,
      0
    ),
    gairBalighDikrioCount: zonal.reduce(
      (sum, zone) => sum + zone.gairBalighDikrioCount,
      0
    ),
    total: zonal.reduce((sum, zone) => sum + zone.total, 0),
  };

  const zonalWise = {
    zones: zonal.map((zone) => ({
      zoneName: zone.zoneName,
      mardoCount: zone.mardoCount,
      bairaoCount: zone.bairaoCount,
      gairBalighDikraoCount: zone.gairBalighDikraoCount,
      gairBalighDikrioCount: zone.gairBalighDikrioCount,
      total: zone.total,
    })),
    totalCounts,
    totalCount: zonal.length,
  };
  const userReport = {
    totalMardo: totalCounts.mardoCount,
    totalBairao: totalCounts.bairaoCount,
    totalGairBalighDikrao: totalCounts.gairBalighDikraoCount,
    totalGairBalighDikrio: totalCounts.gairBalighDikrioCount,
    totalMumineen: totalCounts.total,
  };

  return apiResponse(
    FETCH,
    "User Report",
    {
      userReport,
      zonalWise,
    },
    res
  );
});

const checkFasalDate = async (arazCityData) => {
  try {
    // const currentDate = new Date(2025, 1, 1);
    const currentDate = new Date();
    const fasalDate = new Date(arazCityData.fasalDate);
    return currentDate > fasalDate;
  } catch (error) {
    return false;
  }
};

const getTimezones = apiHandler(async (req, res) => {
  try {
    const timezones = await TimeZone.find().lean();
    return apiResponse(FETCH, "Timezones List", timezones, res);
  } catch (error) {
    return apiError(CUSTOM_ERROR, "Failed to fetch timezones", null, res);
  }
});

module.exports = {
  getAllArazCities,
  addArazCity,
  getSingleArazCity,
  editArazCity,
  deleteArazCity,
  getArazCityByMiqaat,
  getArazCityReport,
  checkFasalDate,
  getTimezones,
};
