{"name": "ams_frontend", "version": "1.0.0", "private": true, "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\""}, "dependencies": {"@ant-design/colors": "^7.0.2", "@ant-design/icons": "^5.3.1", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@fontsource/inter": "^5.0.17", "@fontsource/poppins": "^5.0.12", "@fontsource/public-sans": "^5.0.17", "@fontsource/roboto": "^5.0.12", "@hookform/resolvers": "^3.10.0", "@mui/base": "^5.0.0-beta.38", "@mui/icons-material": "^5.15.19", "@mui/lab": "^5.0.0-alpha.167", "@mui/material": "^5.15.12", "@mui/system": "^5.15.12", "@mui/x-date-pickers": "^8.5.3", "@react-pdf/renderer": "^4.3.0", "@reduxjs/toolkit": "^2.2.5", "@svgr/webpack": "^8.1.0", "@tinymce/tinymce-react": "^5.1.1", "@vitejs/plugin-react": "^4.2.1", "apexcharts": "^3.49.0", "axios": "^1.7.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "formik": "^2.4.5", "framer-motion": "^11.0.8", "froala-editor": "^4.5.2", "html-react-parser": "^5.2.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.515.0", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "papaparse": "^5.5.2", "process": "^0.11.10", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^6.0.0", "react-chartjs-2": "^5.3.0", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-froala-wysiwyg": "^4.5.2", "react-helmet": "^6.1.0", "react-hook-form": "^7.51.5", "react-icons": "^5.2.1", "react-onesignal": "^3.0.1", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router": "^6.22.3", "react-router-dom": "^6.22.3", "recharts": "^2.15.1", "redux": "^5.0.1", "redux-persist": "^6.0.0", "simplebar-react": "^3.2.4", "slick-carousel": "^1.8.1", "swr": "^2.2.5", "util": "^0.12.5", "video.js": "^8.21.0", "vite": "^5.2.10", "vite-jsconfig-paths": "^2.0.1", "web-vitals": "^3.5.2", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/eslint-parser": "^7.23.10", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.2.5", "react-error-overlay": "6.0.11"}}