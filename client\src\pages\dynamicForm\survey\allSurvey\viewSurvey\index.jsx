import { Box, Grid, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { get } from 'lodash';
import AvatarComponent from 'pages/component/avatar';
import BadgeInputComponent from 'pages/component/badge';
import ShowHTMLContent from 'pages/component/InputFields/htmlContent';
import InviteesCriteriaCard from 'pages/component/meetings/criteria';
import PageTitle from 'pages/component/pageTitle';
import TableComponent from 'pages/component/table/table';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getSingleAllSurveyAction, getSingleSurveyAction } from 'redux/actions/surveyAction';
// Get timezone config from localStorage
const timeZoneConfig = JSON.parse(localStorage.getItem('selectedArazCityTimeZone'));
import { capitalizeFirstLetter, convertDateToStringFormat, formatTimeOnly, getImageWithBaseUrl } from 'utils/helper';
import { convertUTCToTimezone } from 'utils/timeZoneHelper';

const QuestionsTable = ({ questions }) => {
  return (
    <MainCard>
      <Box>
        <Grid container sx={{ p: 2, borderBottom: '1px solid #ccc' }} position={'sticky'}>
          <Grid item xs={8}>
            <Typography variant="h6" color={'secondary'} fontWeight={600}>
              Questions
            </Typography>
          </Grid>
          <Grid item xs={4}>
            <Typography variant="h6" color={'secondary'} fontWeight={600}>
              Answer Type
            </Typography>
          </Grid>
        </Grid>
        <Box maxHeight={400} overflow="auto">
          {questions?.map((item, index) => (
            <Grid container key={index} sx={{ p: 2, borderBottom: '1px solid #eee' }}>
              <Grid item xs={8}>
                <ShowHTMLContent value={item?.title} />
              </Grid>
              <Grid item xs={4}>
                <Typography variant="body1">{capitalizeFirstLetter(item?.questionType)}</Typography>
              </Grid>
            </Grid>
          ))}
        </Box>
      </Box>
    </MainCard>
  );
};

const renderLabelValue = (label, value, type) => {
  let renderedValue = value;

  switch (type) {
    case 'badge':
      renderedValue = <BadgeInputComponent badgeContent={value} />;
      break;
    case 'time':
      renderedValue = formatTimeOnly(value, timeZoneConfig);
      break;
    case 'date':
      renderedValue = timeZoneConfig ? convertUTCToTimezone(value, timeZoneConfig, 'DD/MM/YYYY') : convertDateToStringFormat(value);
      break;
    default:
      renderedValue = <Typography variant="subtitle1">{value}</Typography>;

      break;
  }

  return (
    <Box>
      <Typography variant="subtitle1" className="mb-10" sx={{ color: '#690A00' }}>
        {label}
      </Typography>

      <Box className="mb-10">{renderedValue}</Box>
    </Box>
  );
};

const renderCreatedBySection = (surveyData) => (
  <MainCard border darkTitle>
    <Box className="view-meeting-details flex-container" sx={{ height: 'max-content', justifyContent: 'space-between' }}>
      <Box>
        {renderLabelValue('Created By:', '')}
        <Box className="flex-container g-8">
          <AvatarComponent
            src={getImageWithBaseUrl(get(surveyData, 'createdBy.logo'))}
            alt={get(surveyData, 'createdBy.name')}
            height={40}
            width={40}
          />
          <Box ml={1}>
            <Typography variant="subtitle1">{get(surveyData, 'createdBy.name', '')}</Typography>
            <Box className="flex-container g-10">
              <Typography>{get(surveyData, 'createdBy.ITSID')}</Typography>
              <Typography>{get(surveyData, 'createdBy.miqaats.hierarchyPositionID.name')}</Typography>
            </Box>
          </Box>
        </Box>
      </Box>
      {renderLabelValue(
        'Frequency',
        get(surveyData, 'frequency') === 'once' ? 'Once in Season' : capitalizeFirstLetter(get(surveyData, 'frequency', '-'))
      )}
      {get(surveyData, 'frequency') === 'once' && renderLabelValue('Start Date', get(surveyData, 'startDate'), 'date')}
      {renderLabelValue('Start Time', get(surveyData, 'startTime'), 'time')}
      {renderLabelValue('End Time', get(surveyData, 'endTime'), 'time')}
      {renderLabelValue('Status', get(surveyData, 'isActive') ? 'active' : 'inactive', 'badge')}
    </Box>
  </MainCard>
);

const ViewSurvey = ({ closeViewModal, surveyID }) => {
  const dynamicFormData = useSelector((state) => state.dynamicForm);
  const [surveyData, setSurveyData] = useState({});
  const dispatch = useDispatch();

  useEffect(() => {
    if (surveyID) dispatch(getSingleAllSurveyAction(surveyID));
  }, [surveyID]);

  useEffect(() => {
    let { singleAllSurvey } = dynamicFormData || {};
    let surveyRecipientCriteria = get(singleAllSurvey, 'recipients.criteria', [])?.map((item) => {
      return {
        ...item,
        arazCity: item?.arazCityID?.name,
        recipientType: item?.recipientType,
        department: item?.departmentIDs?.map((dept) => dept.name),
        position: item?.positionIDs?.map((pos) => pos.name),
        zone: item?.zoneIDs?.map((zone) => zone.name),
        kgType: item?.kgTypeIDs?.map((kgType) => kgType.name),
        kgGroup: item?.kgGroupIDs?.map((kgGroup) => kgGroup.name),
        ITSIDs: item?.ITSIDs?.map((id) => id),
        total: item?.members?.length || 0
      };
    });
    let surveyReportCriteria = get(singleAllSurvey, 'reportRecipients.criteria', [])?.map((item) => {
      return {
        ...item,
        arazCity: item?.arazCityID?.name,
        recipientType: item?.recipientType,
        department: item?.departmentIDs?.map((dept) => dept.name),
        position: item?.positionIDs?.map((pos) => pos.name),
        zone: item?.zoneIDs?.map((zone) => zone.name),
        kgType: item?.kgTypeIDs?.map((kgType) => kgType.name),
        kgGroup: item?.kgGroupIDs?.map((kgGroup) => kgGroup.name),
        ITSIDs: item?.ITSIDs?.map((id) => id),
        total: item?.members?.length || 0
      };
    });

    setSurveyData({
      ...singleAllSurvey,
      surveyRecipientCriteria: surveyRecipientCriteria,
      surveyRecipientsSystemRoles: singleAllSurvey?.recipients?.systemRoleIDs,
      surveyReportCriteria: surveyReportCriteria,
      surveyReportSystemRoles: singleAllSurvey?.reportRecipients?.systemRoleIDs
    });
  }, [dynamicFormData?.singleAllSurvey]);

  const headers = [
    {
      name: 'logo',
      type: 'image',
      title: '',
      width: '100px'
    },
    {
      name: 'ITSID',
      type: 'text',
      title: 'ITSID',
      sortingactive: true,
      width: '100px'
    },
    {
      name: 'name',
      type: 'text',
      title: 'Name',
      sortingactive: true,
      width: '300px'
    },
    {
      name: 'phone',
      type: 'text',
      title: 'Phone Number',
      sortingactive: true,
      width: '300px'
    }
  ];

  return (
    <>
      <PageTitle title="View Survey" />
      <Grid container spacing={3}>
        <Grid item xs={12}>
          {renderCreatedBySection(surveyData)}
        </Grid>

        <Box mt={2} ml={3} width={'100%'}>
          {surveyData?.surveyRecipients?.length <= 5 ? (
            <MainCard>
              <TableComponent
                tableHeading="Participants"
                defaultPage={5}
                hidePageTitle={true}
                columns={headers}
                rows={surveyData?.surveyRecipients || []}
                allRows={surveyData?.surveyRecipients || []}
                hideRemoveButton={true}
                enablePagination={true}
              />
            </MainCard>
          ) : (
            <InviteesCriteriaCard
              inviteesCriteria={surveyData?.surveyRecipientCriteria}
              systemRoles={surveyData?.surveyRecipientsSystemRoles}
              title="Participants Criteria"
              totalMembers={surveyData?.surveyRecipients?.length}
            />
          )}
        </Box>
        <Box mt={2} ml={3} width={'100%'}>
          {surveyData?.reportRecipientMembers?.length <= 5 ? (
            <MainCard>
              <TableComponent
                tableHeading="View Report Participants"
                defaultPage={5}
                hidePageTitle={true}
                columns={headers}
                rows={surveyData?.reportRecipientMembers || []}
                allRows={surveyData?.reportRecipientMembers || []}
                hideRemoveButton={true}
                enablePagination={true}
              />
            </MainCard>
          ) : (
            <InviteesCriteriaCard
              inviteesCriteria={surveyData?.surveyReportCriteria}
              systemRoles={surveyData?.surveyReportSystemRoles}
              title="View Report Permission"
              totalMembers={surveyData?.surveyRecipients?.length}
            />
          )}
        </Box>
        <Box mt={2} ml={3} width={'100%'}>
          <QuestionsTable questions={surveyData?.questions} />
        </Box>
      </Grid>
    </>
  );
};

export default ViewSurvey;
