import { Box, Button, Typography } from '@mui/material';
import MainCard from 'components/MainCard';
import { get, isEmpty, set } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import { getKGByITSAction } from 'redux/actions/kgAction';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import { getMeetingsDetailsAction, sendAgendaNotificationAction, sendNotificationAction } from 'redux/actions/meetingAction';
import { getImageWithBaseUrl, getItemFromLocalStorage, getPageUrl, notification } from 'utils/helper';
import AvatarComponent from '../../avatar';
import moment from 'moment';
import PageTitle from '../../pageTitle';
import { CommunicationMeetingPageUrl, timeZoneConfig } from 'utils/constant';
import TableComponent from '../../table/table';
import SelectMiqaatAndArazCity from '../../SelectMiqaatAndArazCity';
import AgendaList from '../showAgenda';
import Loader from 'components/Loader';
import { Link } from 'react-router-dom';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import logo from '../../../../assets/images/ams-logo.png';
import BackdropLoader from 'components/BackdropLoader';
import InviteesCriteriaCard from '../criteria';
import SystemRolesCard from '../AgendaCriteriaCard';
import { useAnalytics } from 'utils/userAnalytics';
import { convertUTCDateAndTimeToTimezone, convertUTCToTimezone } from 'utils/timeZoneHelper';

const ViewMeetingComponent = ({ tableHeaders, handleCancel, tableHeader, meetingID }) => {
  const exportRef = useRef(null);
  const dispatch = useDispatch();
  const [host, setHost] = useState();
  const [meetingMembers, setMeetingMembers] = useState([]);
  const meetingDetails = useSelector((state) => state.meeting);
  const [allAgenda, setAllAgendas] = useState([]);
  const [miqaatAndArazCity, setMiqaatAndArazCity] = useState({});
  const [isExporting, setIsExporting] = useState(false);
  const [invitiesCriteria, setInvitiesCriteria] = useState({});
  const [totalMembers, setTotalMembers] = useState(0);
  const [systemRoles, setSystemRoles] = useState([]);
  const { trackUserActivity } = useAnalytics();

  useEffect(() => {
    const data = get(meetingDetails, 'singleMeeting.meetingMembers', []).map((item) => {
      return {
        ...item,
        department: get(item, 'department'),
        zone: get(item, 'zone'),
        arazCity: get(item, 'arazCity'),
        position: get(item, 'hierarchyPosition')
      };
    });
    let agendas = get(meetingDetails, 'singleMeeting.agendas', [])?.map((item) => ({
      ...item,
      isEdit: true,
      proceeding: item?.proceedings,
      members: item?.assigneeMembers?.map((member) => ({
        ...member,
        department: member?.departmentID?.name,
        zone: member?.arazCityZoneID?.name,
        position: member?.hierarchyPositionID?.name
      })),
      memberType: item?.assignee.map((assignee) => assignee?.memberType)
    }));
    let invitees = get(meetingDetails, 'singleMeeting.invities', [])?.map((item) => {
      return {
        ...item,
        arazCity: item?.arazCityID?.name,
        recipientType: item?.memberType,
        department: item?.departmentIDs?.map((dept) => dept.name),
        position: item?.positionIDs?.map((pos) => pos.name),
        zone: item?.zoneIDs?.map((zone) => zone.name),
        kgType: item?.kgTypeIDs?.map((kgType) => kgType.name),
        kgGroup: item?.kgGroupIDs?.map((kgGroup) => kgGroup.name),
        ITSIDs: item?.ITSIDs?.map((id) => id),
        total: item?.members?.length || 0
      };
    });

    const membersCount = invitees.map((item) => item?.members?.length).reduce((a, b) => a + b, 0);

    const systemRoles = get(meetingDetails, 'singleMeeting.systemRoles', []).map((item) => item);

    setTotalMembers(membersCount);

    setSystemRoles(systemRoles);

    setHost(get(meetingDetails, 'singleMeeting.hostedBy', {}));
    setInvitiesCriteria([...invitees]);
    setAllAgendas([...agendas]);
    setMeetingMembers([...data]);
  }, [meetingDetails?.singleMeeting]);

  useEffect(() => {
    trackUserActivity({
      action: 'page_visit',
      page: `${get(meetingDetails, 'singleMeeting.title')}-Meeting Report`,
      module: 'Communication'
    });
  }, []);

  const headers = [
    {
      name: 'logo',
      type: 'image',
      title: '',
      sortingactive: false,
      minWidth: '60px'
    },
    {
      name: 'ITSID',
      type: 'text',
      title: 'ITS ID',
      sortingactive: true,
      minWidth: '100px'
    },
    {
      name: 'name',
      type: 'text',
      title: 'Name',
      sortingactive: true,
      minWidth: '260px'
    },
    {
      name: 'arazCity',
      type: 'text',
      title: 'Araz City',
      sortingactive: true,
      minWidth: '150px'
    },
    {
      name: 'department',
      type: 'text',
      title: 'Department',
      sortingactive: true,
      minWidth: '150px'
    },
    {
      name: 'position',
      type: 'text',
      title: 'Position',
      sortingactive: true,
      minWidth: '150px'
    },
    {
      name: 'zone',
      type: 'text',
      title: 'Zone',
      sortingactive: true,
      minWidth: '150px'
    }
  ];

  const handleSendNotification = async () => {
    const response = await dispatch(
      sendNotificationAction({
        type: 'SEND_MOM_REMINDER',
        meetingID: get(meetingDetails, 'singleMeeting._id')
      })
    );
    if (get(response, 'payload.success')) {
      dispatch(notification(true, 'Notification sent successfully!', false));
    }
  };

  const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  // Helper function to check if images are loaded
  const areImagesLoaded = (element) => {
    const images = element.getElementsByTagName('img');
    for (let i = 0; i < images.length; i++) {
      if (!images[i].complete) {
        return false;
      }
    }
    return true;
  };

  const handleExport = async () => {
    setIsExporting(true);
    try {
      const input = exportRef.current;
      if (!input) throw new Error('Export element not found');

      await sleep(1000); // optional delay

      const fullCanvas = await html2canvas(input, {
        scale: 2,
        useCORS: true,
        backgroundColor: '#fff',
        ignoreElements: (element) => {
          return element.classList.contains('invitees-section');
        }
      });

      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      const marginTop = 10;
      const marginBottom = 10;
      const headerHeight = 18;
      const footerHeight = 8;
      const contentHeight = pageHeight - marginTop - marginBottom - headerHeight - footerHeight;

      const imgWidth = pageWidth;
      const imgHeight = (fullCanvas.height * imgWidth) / fullCanvas.width;

      const rowHeight = 80; // Adjust to your row height in pixels
      const approxPageCanvasHeight = (contentHeight * fullCanvas.width) / imgWidth;
      const rowsPerPage = Math.floor(approxPageCanvasHeight / rowHeight);
      const safeCanvasHeight = rowsPerPage * rowHeight;

      const logoUrl = logo;

      const logoImg = new Image();
      logoImg.src = logoUrl;

      await new Promise((resolve) => {
        logoImg.onload = resolve;
      });

      const aspectRatio = logoImg.width / logoImg.height;
      const logoHeight = 13;
      const logoWidth = logoHeight * aspectRatio;

      const timestamp = moment().format('DD/MM/YYYY hh:mm A');

      let renderedHeight = 0;
      let pageCount = 0;

      while (renderedHeight < fullCanvas.height) {
        const pageCanvas = document.createElement('canvas');
        const context = pageCanvas.getContext('2d');

        pageCanvas.width = fullCanvas.width;
        pageCanvas.height = Math.min(safeCanvasHeight, fullCanvas.height - renderedHeight);

        context.drawImage(fullCanvas, 0, renderedHeight, fullCanvas.width, pageCanvas.height, 0, 0, fullCanvas.width, pageCanvas.height);

        const imgData = pageCanvas.toDataURL();
        if (pageCount > 0) pdf.addPage();

        const headerY = 0;
        const contentY = headerHeight + 2;

        // Logo positioning (left-aligned)
        const logoX = 5; // Left margin
        const logoY = headerY + (headerHeight - logoHeight) / 2;
        pdf.addImage(logoImg, 'PNG', logoX, logoY, logoWidth, logoHeight);

        // Date positioning (right-aligned)
        pdf.setFontSize(7);
        pdf.setTextColor(100, 100, 100);
        const textWidth = pdf.getTextWidth(timestamp);
        const dateX = pageWidth - textWidth - 5; // Right margin
        const dateY = headerY + (headerHeight + 9) / 2 - 2; // Vertically centered
        pdf.text(timestamp, dateX, dateY);

        // Footer
        const footerY = pageHeight - marginBottom - footerHeight + 5;

        // Footer text (date/time) - left side
        pdf.setFontSize(7);
        pdf.setTextColor(150, 150, 150);
        // pdf.text(timestamp, 5, footerY);

        // Page number - right side
        pdf.text(`Page ${pageCount + 1}`, pageWidth - 20, footerY);

        // Page content
        pdf.addImage(imgData, 'PNG', 0, contentY, imgWidth, (pageCanvas.height * imgWidth) / fullCanvas.width);

        renderedHeight += pageCanvas.height;
        pageCount++;
      }

      pdf.save(`${get(meetingDetails, 'singleMeeting.title')}.pdf`);
    } catch (error) {
      console.error('PDF export failed:', error);
      alert('Failed to export PDF. See console for details.');
    } finally {
      setIsExporting(false);
      trackUserActivity({
        action: 'download_report',
        page: `${get(meetingDetails, 'singleMeeting.title')}`,
        module: 'Communication'
      });
    }
  };

  const sendAgendaNotification = async (agenda) => {
    let response = await dispatch(sendAgendaNotificationAction({ meetingID: meetingID, agendaID: agenda?._id }));
  };
  const meetingUrl = get(meetingDetails, 'singleMeeting.meetingUrl') || '';
  const { singleMeeting = {} } = meetingDetails || {};
  const { date, time = {} } = singleMeeting || {};
  const { hh = '', mm = '', period = '' } = time;

  return (
    <>
      {meetingDetails?.loading && <Loader />}
      {isExporting && <BackdropLoader />}
      <SelectMiqaatAndArazCity
        hideSearchField={true}
        values={miqaatAndArazCity}
        onSelectChange={(data) => {
          setMiqaatAndArazCity({ ...data });
        }}
      />
      <PageTitle title={get(meetingDetails, 'singleMeeting.title')} showBackButton handleCancel={handleCancel} />
      <Box maxWidth={'100%'}>
        <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '12px' }}>
          <Button variant="contained" color="primary" className="icon-btn" startIcon={<FileDownloadOutlinedIcon />} onClick={handleExport}>
            Export Excel
          </Button>
        </div>

        <Box className="d-flex-column g-20 view-meeting-wrapper" ref={exportRef}>
          {/* {
            !isExporting &&
            <div className="ams-logo">
              <img src={logo} alt="Logo" className="logo" />
              <Typography variant="subtitle1" className="mb-10">Ashara Management System</Typography>
            </div>
          } */}

          <MainCard border={true} darkTitle={true} mainWrapperClassName="w-90">
            {isExporting && <PageTitle title={get(meetingDetails, 'singleMeeting.title')} />}
            <div className="view-meeting-details flex-container" style={{ height: 'max-content', justifyContent: 'space-between' }}>
              <div>
                <Typography variant="subtitle1" className="mb-10" style={{ color: '#690A00' }}>
                  Hosted By:
                </Typography>
                <div className="flex-container g-10">
                  <AvatarComponent src={getImageWithBaseUrl(get(host, 'logo'))} alt={get(host, 'name')} height={40} width={40} />
                  <div>
                    <Typography variant="subtitle1">{get(host, 'name')}</Typography>
                    <div className="flex-container g-10">
                      <Typography>{get(host, 'ITSID')}</Typography>
                      <Typography>{get(host, 'miqaats.hierarchyPositionID.name')}</Typography>
                    </div>
                  </div>
                </div>
              </div>

              {get(meetingDetails, 'singleMeeting.meetingUrl') && (
                <Box maxWidth={'300px'}>
                  <Typography variant="subtitle1" className="mb-10" style={{ color: '#690A00' }}>
                    Online Meeting Link
                  </Typography>
                  <a href={meetingUrl} className="link" target="_blank" rel="noopener noreferrer">
                    <Typography>{meetingUrl}</Typography>
                  </a>
                </Box>
              )}
              {get(meetingDetails, 'singleMeeting.meetingAddress') && (
                <Box maxWidth={'300px'}>
                  <Typography variant="subtitle1" className="mb-10" style={{ color: '#690A00' }}>
                    Offline Meeting Address
                  </Typography>
                  <Typography>{get(meetingDetails, 'singleMeeting.meetingAddress')}</Typography>
                </Box>
              )}
              <Box>
                <Typography variant="subtitle1" className="mb-10" style={{ color: '#690A00' }}>
                  Date and Time
                </Typography>
                <Typography>
                  {isEmpty(date) && isEmpty(time) ? (
                    '-'
                  ) : (
                    <>
                      {!isEmpty(date) && !isEmpty(time)
                        ? convertUTCDateAndTimeToTimezone(date, time, timeZoneConfig)
                        : !isEmpty(date)
                          ? convertUTCToTimezone(date, timeZoneConfig, 'dddd, DD MMMM YYYY')
                          : ''}
                    </>
                  )}
                </Typography>
              </Box>
            </div>
          </MainCard>

          {get(meetingDetails, 'singleMeeting.mom') && (
            <MainCard>
              <div className="d-flex">
                <Typography variant="subtitle1" color="primary">
                  Minutes of Meetings
                </Typography>
                <Button color="primary" variant="outlined" onClick={handleSendNotification}>
                  Send Notification
                </Button>
              </div>
              <Typography dangerouslySetInnerHTML={{ __html: get(meetingDetails, 'singleMeeting.mom') }} />
            </MainCard>
          )}
          <Box>
            {meetingMembers?.length > 5 ? (
              <InviteesCriteriaCard inviteesCriteria={invitiesCriteria} systemRoles={systemRoles} totalMembers={meetingMembers?.length} />
            ) : (
              <MainCard>
                <Typography variant="subtitle1" color="primary">
                  Invitees
                </Typography>
                <TableComponent
                  columns={!isEmpty(tableHeaders) ? tableHeaders : headers}
                  rows={meetingMembers}
                  allRows={meetingMembers}
                  module="meeting"
                  defaultPage={5}
                  enablePagination={isExporting ? false : true}
                  tableContainerClassName={isExporting ? 'export-table-container' : ''}
                />
              </MainCard>
            )}
          </Box>
          <div className="d-flex-column g-10 ">
            <AgendaList allAgendas={[{ agenda: 'Tilawat and Dua' }]} hideAction={true} isEdit={false} />
          </div>
          <div className="d-flex-column g-10">
            <AgendaList
              showCriteria={true}
              allAgendas={allAgenda}
              isView={true}
              inviteesCriteria={invitiesCriteria}
              totalMembers={meetingMembers?.length}
              showSendAgendaNotification={true}
              sendAgendaNotification={sendAgendaNotification}
              isExporting={isExporting}
            />
          </div>
        </Box>
      </Box>
    </>
  );
};

export default ViewMeetingComponent;
