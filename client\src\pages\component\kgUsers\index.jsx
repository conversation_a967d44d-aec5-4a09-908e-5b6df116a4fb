import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import FilterListIcon from '@mui/icons-material/FilterList';
import { Button, Dialog, DialogActions, DialogContent, Grid, Stack, Typography } from '@mui/material';
import { Box } from '@mui/system';
import Loader from 'components/Loader';
import { get, isEmpty } from 'lodash';
import moment from 'moment';
import CustomModal from 'pages/component/customModal';
import ConfirmationModal from 'pages/component/customModal/confirmationModal';
import LargeDataTable from 'pages/component/table';
import ActionButtons from 'pages/component/table/tableComponent/actionButtons';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAllJamaatsAction } from 'redux/actions/arazCityAction';
import { getActiveDepartmentByArazCityAction } from 'redux/actions/departmentsAction';
import { getFunctionsByDepartmentAction } from 'redux/actions/functionAction';
import { getAliasNameAction } from 'redux/actions/hierarchyActions';
import { getAllHierarchyPositions } from 'redux/actions/hierarchyPositionAction';
import { deleteKGAction, getAllKGAction, getAllKGExportRecordAction, syncITSDataAction } from 'redux/actions/kgAction';
import { getAllKGGroupAction } from 'redux/actions/kgGroupAction';
import { getAllKGTypeAction } from 'redux/actions/kgtypeAction';
import { getSystemRolesAction } from 'redux/actions/systemRoleAction';
import { getZoneyByArazCityAction } from 'redux/actions/zoneAction';
import { setAllJamatAction } from 'redux/reducers/arazCityReducer';
import { setFunctionsForDepAction } from 'redux/reducers/functionsReducer';
import { setITSIDDetailAction, setKGListAction } from 'redux/reducers/kgReducer';
import { getUserDetail } from 'utils/auth';
import { SUPERADMIN } from 'utils/constant';
import { getOptions, isQasreAliFemale, notification, placeholderImg, showMiqaatValidationAlert } from 'utils/helper';
import { renderField } from '../InputFields';
import AddEditKGUser from './addEditKG';
import { exportToExcel, KGFilters, kglistHeaders, KGUserStatusTab } from './constant';

import { useAnalytics } from 'utils/userAnalytics';
import { convertUTCToTimezone } from 'utils/timeZoneHelper';
const KGUsers = () => {
  const allKgList = useSelector((state) => state.kgData);
  const departmentData = useSelector((state) => state.departments);
  const systemData = useSelector((state) => state.system);
  const zoneData = useSelector((state) => state.zone);
  const [kgList, setKGList] = useState([]);
  const [kgAllList, setAllKGList] = useState([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const openFilter = () => setIsFilterOpen(!isFilterOpen);
  const [deleteKGID, setDeleteKGID] = useState('');
  const [page, setPage] = useState(0);
  const [totalCount, setTotalCount] = useState(1);
  const [limit, setLimit] = useState(50);

  const [searchValues, setSearchValues] = useState({
    status: 'all'
  });
  const [addEditModal, setAddEditModal] = useState({
    open: false,
    id: ''
  });
  const [viewUserModal, setViewUserModal] = useState({
    open: false,
    id: ''
  });
  const [hideTableBody, setHideTableBody] = useState(true);
  const [miqaatAndArazCity, setMiqaatAndArazCity] = useState({});
  const [functionsData, setFunctionsData] = useState([]);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const arazCityData = useSelector((state) => state.arazCity);
  const { trackUserActivity } = useAnalytics();
  const timeZoneConfig = JSON.parse(localStorage.getItem('selectedArazCityTimeZone'));

  useEffect(() => {
    dispatch(getAllHierarchyPositions());
    dispatch(getAllKGTypeAction());
    dispatch(getAllKGGroupAction());
    dispatch(getSystemRolesAction());
  }, []);

  useEffect(() => {
    if (miqaatAndArazCity?.arazCity) {
      let selectedJamiaat = arazCityData?.arazCityForSingleMiqaat?.find((arazCity) => arazCity?._id === miqaatAndArazCity?.arazCity);
      if (!isEmpty(selectedJamiaat?.jamiats)) {
        dispatch(getAllJamaatsAction({ ids: selectedJamiaat?.jamiats || [] }));
      } else {
        dispatch(setAllJamatAction([]));
      }
    }
  }, [miqaatAndArazCity?.arazCity, arazCityData?.arazCityForSingleMiqaat]);

  useEffect(() => {
    if (miqaatAndArazCity?.arazCity) {
      dispatch(getActiveDepartmentByArazCityAction(miqaatAndArazCity?.arazCity));
      dispatch(getZoneyByArazCityAction(miqaatAndArazCity?.arazCity));
    }
  }, [miqaatAndArazCity?.arazCity]);

  const getFunction = async (department) => {
    let response = await dispatch(getFunctionsByDepartmentAction(department));
    let updatedData = get(response, 'payload.data', []);
    setFunctionsData(updatedData || []);
  };

  const onPageChange = (newPage) => {
    setPage(newPage);
  };

  const onPageLimitChange = (newLimit) => {
    setLimit(newLimit);
    setPage(0);
  };

  const getSortKey = (key) => {
    switch (key) {
      case 'jamaatName':
        return 'jamaat';
      case 'jamaitName':
      case 'jamiat':
      default:
        return key || '';
    }
  };

  const getKGList = async (selectedFilters) => {
    setLoading(true);
    let paylaod = {
      miqaat: miqaatAndArazCity?.miqaat,
      arazCity: miqaatAndArazCity?.arazCity,
      page: page ? page + 1 : 1,
      sortBy: getSortKey(searchValues?.sortBy) || '',
      sortOrder: searchValues?.sortOrder,
      search: searchValues?.search || '',
      status: searchValues?.status !== 'all' ? searchValues?.status : '',
      limit: limit || 50
    };
    if (selectedFilters) {
      paylaod = { ...paylaod, ...selectedFilters };
    }
    let response = await dispatch(getAllKGAction(paylaod));

    if (response?.payload?.success) {
      const kgData = get(response, 'payload.data', {});
      setTotalCount(get(kgData.pagination, 'totalCount', 0));
    }
    setIsFilterOpen(false);
    setLoading(false);
    setHideTableBody(false);
  };

  useEffect(() => {
    if (miqaatAndArazCity?.arazCity && miqaatAndArazCity?.miqaat) {
      dispatch(getAliasNameAction({ arazCityID: miqaatAndArazCity?.arazCity }));
      getKGList(selectedFilters);
    } else {
      setHideTableBody(true);
      dispatch(setKGListAction([]));
    }
  }, [miqaatAndArazCity?.arazCity, miqaatAndArazCity?.miqaat, page, limit, searchValues]);

  useEffect(() => {
    if (miqaatAndArazCity?.arazCity && miqaatAndArazCity?.miqaat) {
      dispatch(getAliasNameAction({ arazCityID: miqaatAndArazCity?.arazCity }));
      trackUserActivity({
        action: 'page_visit',
        page: 'KG List',
        module: 'HR & Hierarchy'
      });
    }
  }, [miqaatAndArazCity?.arazCity, miqaatAndArazCity?.miqaat]);

  const formatKGData = (data) => {
    let user = getUserDetail();

    return data?.map((item) => ({
      ...item,
      isSuperAdmin: user?.role === SUPERADMIN,
      arazCityZone: get(item, 'arazCityZone.name', ''),
      department: get(item, 'department.name', ''),
      zone: get(item, 'arazCityZone.name', ''),
      kgType: get(item, 'kgType.name', ''),
      kgGroup: get(item, 'kgGroup.name', ''),
      jamiatName: get(item, 'jamiat.name', ''),
      jamaatName: get(item, 'jamaat.name', ''),
      hierarchyPosition: arazCityData?.showPositionAlias
        ? get(item, 'hierarchyPosition.alias', '')
        : get(item, 'hierarchyPosition.name', ''),
      function: get(item, 'otherFunction', '') || get(item, 'function.name', ''),
      isNotLoggedInUser: (item?.isActive && item?._id !== user?.userId) || false,
      status: get(item, 'status', ''),
      gender: get(item, 'gender', '') === 'M' ? 'Male' : get(item, 'gender', '') === 'F' ? 'Female' : '',
      cityRole: get(item, 'cityRole.name', ''),
      updatedAt: item?.updatedAt && timeZoneConfig ? convertUTCToTimezone(item.updatedAt, timeZoneConfig, 'DD-MM-YYYY HH:mm', true) : '',
      createdAt: item?.createdAt && timeZoneConfig ? convertUTCToTimezone(item.createdAt, timeZoneConfig, 'DD-MM-YYYY HH:mm', true) : '',
      phone: get(item, 'phone'),
      logo: isQasreAliFemale(get(item, 'ITSID'), get(item, 'gender')) ? placeholderImg(get(item, 'name', '')) : get(item, 'logo'),
      appDetail: get(item, 'appDetails.deviceType')
        ? `${get(item, 'appDetails.deviceType', '')} - ${get(item, 'appDetails.version', '')}`
        : 'Not Installed',
      razaStatus: get(item, 'razaStatus.RazaStatus') === 'Has Raza',
      razaStatusName: get(item, 'razaStatus.RazaStatus') === 'Has Raza' ? 'Has Raza' : 'No Raza',
      miqaatZone: get(item, 'razaStatus.MiqaatZone') || '-'
    }));
  };

  const transformedKgData = useMemo(() => {
    let updatedKGUser = formatKGData(allKgList?.kgList?.users);
    return updatedKGUser;
  }, [allKgList?.kgList, arazCityData?.showPositionAlias]);

  const FilterField = useMemo(
    () => [
      {
        name: 'hierarchyPosition',
        type: 'autoselect',
        label: 'Hierarchy Position',
        placeholder: 'Select Hierarchy Position',
        multiple: true,
        items: getOptions(allKgList?.allHierarchyPosition, arazCityData?.showPositionAlias ? 'alias' : 'name')
      },
      {
        name: 'cityRole',
        type: 'autoselect',
        label: 'System Permission',
        placeholder: 'Select System Permission',
        multiple: true,
        items: getOptions(systemData?.systemRoles, 'name')
      },
      {
        name: 'jamaat',
        type: 'autoselect',
        label: 'Jamaat',
        placeholder: 'Select Jamaat',
        multiple: true,
        items: getOptions(arazCityData?.allJamaats, 'name')
      },
      {
        name: 'department',
        type: 'autoselect',
        label: 'Department',
        placeholder: 'Select Department',
        multiple: true,
        items: getOptions(departmentData?.departmentsforArazCity, 'name', 'id')
      },
      {
        name: 'zone',
        type: 'autoselect',
        label: 'Zone',
        placeholder: 'Select Zone',
        multiple: true,
        items: getOptions(zoneData?.zoneForSingleArazCity, 'name')
      },
      {
        type: 'text',
        name: 'miqaatZone',
        label: 'Miqaat Zone',
        xl: 6,
        sm: 6,
        multiple: true,
        placeholder: 'Search Miqaat Zone'
      },
      {
        name: 'kgGroup',
        type: 'autoselect',
        label: 'KG Group',
        placeholder: 'Select KG Group',
        multiple: true,
        items: getOptions(allKgList?.allKGGroup, 'name')
      },
      {
        name: 'kgType',
        type: 'autoselect',
        label: 'KG Type',
        placeholder: 'Select KG Type',
        multiple: true,
        items: getOptions(allKgList?.allKGType, 'name')
      },
      {
        name: 'status',
        type: 'autoselect',
        label: 'Status',
        placeholder: 'Select Status',
        multiple: true,
        items: KGUserStatusTab
      },
      {
        name: 'deviceType',
        type: 'autoselect',
        label: 'App Installed Status',
        placeholder: 'Select Status',
        multiple: true,
        items: [
          { label: 'Android', value: 'ANDROID' },
          { label: 'iOS', value: 'IOS' },
          { label: 'Not Installed', value: 'not-installed' }
        ]
      },
      {
        name: 'razaStatus',
        type: 'autoselect',
        label: 'Raza Status',
        placeholder: 'Select Status',
        multiple: true,
        items: [
          { label: 'Has Raza', value: 'Has Raza' },
          { label: 'No Raza', value: 'No Raza' }
        ]
      },
      {
        name: 'gender',
        type: 'checkbox-group',
        label: 'Gender',
        placeholder: 'Gender',
        multiple: true,
        row: true,
        items: [
          { label: 'Male', value: 'male' },
          { label: 'Female', value: 'female' }
        ]
      }
    ],
    [allKgList, arazCityData?.showPositionAlias, systemData, departmentData, zoneData, KGUserStatusTab]
  );

  const columnsToFilter = ['hierarchyPosition', 'department', 'function', 'zone', 'kgGroup', 'kgType', 'cityRole', 'status', 'gender'];

  const initialFilterValues = columnsToFilter.reduce((acc, col) => {
    acc[col] = col === 'gender' ? ['male', 'female'] : [];
    return acc;
  }, {});
  const [selectedFilters, setSelectedFilters] = useState({ ...initialFilterValues, gender: ['male', 'female'] });
  const [appliedFilters, setAppliedFilters] = useState({ ...initialFilterValues });

  useEffect(() => {
    setAllKGList(transformedKgData || []);
    setKGList(transformedKgData || []);
    setTotalCount(get(allKgList, 'kgList.pagination.totalCount', 0));
  }, [transformedKgData, arazCityData.showPositionAlias]);

  const closeFilter = () => {
    setSelectedFilters({ ...appliedFilters });
    setIsFilterOpen(false);
  };

  const headers = [
    {
      name: 'logo',
      type: 'image',
      title: '',
      sortingactive: false,
      minWidth: '60px'
    },
    {
      name: 'ITSID',
      type: 'text',
      title: 'ITS ID',
      sortingactive: true,
      minWidth: '100px'
    },
    {
      name: 'name',
      type: 'text',
      title: 'Name',
      sortingactive: true,
      minWidth: '260px'
    },
    {
      name: 'razaStatus',
      type: 'razaStatus',
      title: 'Raza Status',
      minWidth: '100px',
      sortingactive: true
    },
    {
      name: 'miqaatZone',
      type: 'text',
      title: 'Miqaat Zone',
      minWidth: '200px',
      sortingactive: true
    },
    {
      name: 'zone',
      type: 'text',
      title: 'Zone',
      minWidth: '200px',
      sortingactive: true
    },
    {
      name: 'jamiatName',
      type: 'text',
      title: 'Jamiat',
      sortingactive: true,
      minWidth: '200px'
    },
    {
      name: 'jamaatName',
      type: 'text',
      title: 'Jamaat',
      sortingactive: true,
      minWidth: '200px'
    },
    {
      name: 'gender',
      type: 'text',
      title: 'Gender',
      sortingactive: true,
      minWidth: '100px'
    },
    {
      name: 'hierarchyPosition',
      type: 'text',
      title: 'Hierarchy Position',
      sortingactive: true,
      minWidth: '150px'
    },
    {
      name: 'department',
      type: 'text',
      title: 'Department',
      sortingactive: true,
      minWidth: '150px'
    },
    {
      name: 'function',
      type: 'text',
      title: 'Function',
      minWidth: '200px'
    },
    {
      name: 'phone',
      type: 'text',
      title: 'Mobile Number',
      sortingactive: true,
      minWidth: '200px'
    },
    {
      name: 'createdAt',
      type: 'text',
      title: 'Added At',
      sortingactive: true,
      minWidth: '200px'
    },
    {
      name: 'createdBy',
      type: 'text',
      title: 'Added By',
      sortingactive: true,
      minWidth: '200px'
    },
    {
      name: 'updatedAt',
      type: 'text',
      title: 'Updated At',
      sortingactive: true,
      minWidth: '200px'
    },
    {
      name: 'updatedBy',
      type: 'text',
      title: 'Updated By',
      sortingactive: true,
      minWidth: '150px'
    },
    {
      name: 'kgGroup',
      type: 'text',
      title: 'KG Group',
      sortingactive: true,
      minWidth: '160px'
    },
    {
      name: 'kgType',
      type: 'text',
      title: 'KG Type',
      sortingactive: true,
      minWidth: '200px'
    },
    // {
    //   name: 'cityRole',
    //   type: 'text',
    //   title: 'Permission',
    //   minWidth: '200px'
    // },
    {
      name: 'status',
      type: 'badge',
      title: 'Consent',
      sortingactive: true,
      minWidth: '160px'
    },
    {
      name: 'declineReason',
      type: 'info',
      title: 'Reason'
    },
    {
      name: 'appDetail',
      type: 'text',
      title: 'Mobile App',
      minWidth: '200px'
    },
    {
      name: 'sync',
      type: 'actions',
      title: 'Sync ITS Data',
      minWidth: '100px',
      sortingactive: false,
      component: ActionButtons,
      multipleButtons: [
        {
          type: 'button',
          buttonTitle: 'Sync',
          buttonOnClick: async (type, row) => {
            let { ITSID, _id } = row || {};
            let payload = {
              ITSID: ITSID,
              id: _id
            };

            let response = await dispatch(syncITSDataAction(payload));
            if (response?.payload?.success) {
              getKGList(selectedFilters);
            }
          },
          showButton: 'isSuperAdmin',
          color: 'primary',
          tooltip: 'Sync ITS Data'
        }
      ]
    },
    {
      name: 'actions',
      type: 'actions',
      title: 'Actions',
      sortingactive: false,
      component: ActionButtons,
      multipleButtons: [
        {
          type: 'view',
          module: 'kgUser',
          action: 'view',
          buttonOnClick: async (type, row) => {
            const { miqaat, arazCity } = miqaatAndArazCity || {};
            if (miqaat && arazCity) {
              setViewUserModal({ ...viewUserModal, open: true, id: row?._id });
            } else {
              showMiqaatValidationAlert(miqaatAndArazCity, dispatch);
            }
          },
          color: 'secondary',
          tooltip: 'View'
        },
        {
          type: 'edit',
          showButton: 'isNotLoggedInUser',
          buttonOnClick: (type, rowData) => {
            const { miqaat, arazCity } = miqaatAndArazCity || {};
            if (miqaat && arazCity) {
              setAddEditModal({ ...addEditModal, open: true, id: rowData?._id });
            } else {
              showMiqaatValidationAlert(miqaatAndArazCity, dispatch);
            }
          },
          module: 'kgUser',
          action: 'edit',
          color: 'primary',
          tooltip: 'Edit'
        },
        {
          type: 'delete',
          module: 'kgUser',
          showButton: 'isNotLoggedInUser',
          action: 'delete',
          buttonOnClick: (type, rowData) => {
            setDeleteKGID(rowData?._id);
          },
          color: 'secondary',
          tooltip: 'Delete'
        }
      ]
    }
  ];

  const getTitleForColumn = (colName) => {
    return headers.find((h) => h.name === colName)?.title || colName;
  };

  const actionButtons = {
    title: 'Add KG',
    module: 'kgUser',
    onClick: () => {
      const { miqaat, arazCity } = miqaatAndArazCity || {};
      if (miqaat && arazCity) {
        setAddEditModal({ ...addEditModal, open: true, id: '' });
      } else {
        showMiqaatValidationAlert(miqaatAndArazCity, dispatch);
      }
    }
  };

  const handleClose = () => {
    setDeleteKGID('');
  };

  const closeAddEditModal = () => {
    setAddEditModal({ ...addEditModal, open: false, id: '' });
    dispatch(setFunctionsForDepAction([]));
    dispatch(setITSIDDetailAction({}));
  };

  const closeViewUserModal = () => {
    setViewUserModal({ ...viewUserModal, open: false, id: '' });
    dispatch(setFunctionsForDepAction([]));
    dispatch(setITSIDDetailAction({}));
  };

  const handleDelete = async () => {
    const response = await dispatch(
      deleteKGAction({ id: deleteKGID, arazCity: miqaatAndArazCity?.arazCity, miqaat: miqaatAndArazCity?.miqaat })
    );
    if (get(response, 'payload.success', false)) {
      setLoading(false);
      dispatch(
        getAllKGAction({
          miqaat: miqaatAndArazCity?.miqaat,
          arazCity: miqaatAndArazCity?.arazCity,
          page: page ? page + 1 : 1,
          sortBy: getSortKey(searchValues?.sortBy) || '',
          sortOrder: searchValues?.sortOrder,
          search: '',
          status: searchValues?.status !== 'all' ? searchValues?.status : '',
          limit: limit || 50
        })
      );
      setDeleteKGID('');
    }
  };

  const DeleteModalButtons = [
    { label: 'Cancel', onClick: () => handleClose(), color: 'error', disabled: allKgList?.loading },
    { label: 'Confirm', onClick: () => handleDelete(), variant: 'contained', color: 'primary', disabled: allKgList?.loading }
  ];

  const handleSort = (sortingData) => {
    setPage(0);
    setSearchValues({ ...searchValues, sortBy: sortingData?.key, sortOrder: sortingData?.value });
  };

  const handleChangeFilter = (type, value, name) => {
    setSelectedFilters({ ...selectedFilters, [name]: value, functions: [] });
    // if (name === 'department') {
    //   getFunction(value);
    // }
  };

  const handleExportReport = async () => {
    let payload = {
      miqaat: miqaatAndArazCity?.miqaat,
      arazCity: miqaatAndArazCity?.arazCity,
      sortBy: getSortKey(searchValues?.sortBy) || '',
      sortOrder: searchValues?.sortOrder,
      search: searchValues?.search || '',
      status: searchValues?.status !== 'all' ? searchValues?.status : ''
    };
    if (selectedFilters) {
      payload = { ...payload, ...selectedFilters };
    }
    let response = await dispatch(getAllKGExportRecordAction(payload));
    let responseData = get(response, 'payload.data.users', []);
    if (response?.payload?.success) {
      if (!isEmpty(responseData)) {
        exportToExcel(kglistHeaders, formatKGData(responseData || []) || [], 'KGUsers');
        trackUserActivity({
          action: 'download-report',
          page: 'KG List',
          module: 'HR & Hierarchy'
        });
      } else {
        dispatch(notification(false, 'No data found', true));
      }
    } else {
      dispatch(notification(false, response?.payload?.message || 'No data found', true));
    }
  };

  const clearFilter = () => {
    setSelectedFilters(initialFilterValues);
    getKGList(initialFilterValues);
  };

  const handleApplyFilter = () => {
    setPage(0);
    setAppliedFilters({ ...selectedFilters });
    if (miqaatAndArazCity?.miqaat && miqaatAndArazCity?.arazCity) {
      getKGList(selectedFilters);
    } else {
      dispatch(setKGListAction([]));
      setHideTableBody(true);
    }
  };

  return (
    <>
      {allKgList?.loading && <Loader />}
      <Dialog open={isFilterOpen} maxWidth={'lg'} keepMounted fullWidth onClose={closeFilter}>
        <DialogContent>
          {/* <Stack direction='column' justifyContent='center' mb={2} mt={2}> */}
          <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2} mt={2}>
            <Typography variant="h5" color="primary" mb={2}>
              Filters
            </Typography>
            <Button variant="outlined" onClick={() => clearFilter()}>
              Clear All
            </Button>
          </Stack>
          <Grid columnSpacing={2} container>
            {FilterField?.map((field) => {
              return (
                <Grid item xs={12} sm={6} md={6} lg={6}>
                  <Typography variant="h6" fontWeight={500} color="secondary" mb={1}>
                    {field?.label}
                  </Typography>
                  <Box my={1}>{renderField({ ...field }, handleChangeFilter, selectedFilters?.[field?.name])}</Box>
                </Grid>
              );
            })}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeFilter} variant="outlined">
            Cancel
          </Button>
          <Button onClick={() => handleApplyFilter()} variant="contained">
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <CustomModal
        cancel={handleClose}
        buttons={DeleteModalButtons}
        borderRadius="20px"
        Component={<ConfirmationModal title="KG" />}
        open={deleteKGID ? true : false}
      />
      {addEditModal?.open && (
        <CustomModal
          cancel={closeAddEditModal}
          borderRadius="20px"
          fullScreen={true}
          Component={
            <AddEditKGUser
              cityId={miqaatAndArazCity?.arazCity}
              miqaatId={miqaatAndArazCity?.miqaat}
              getKGList={() => getKGList(selectedFilters)}
              KGID={addEditModal?.id}
              closeAddEditModal={closeAddEditModal}
            />
          }
          open={addEditModal?.open ? true : false}
        />
      )}
      {viewUserModal?.open && (
        <CustomModal
          cancel={closeViewUserModal}
          borderRadius="20px"
          fullScreen={true}
          Component={
            <AddEditKGUser
              isViewUser={true}
              cityId={miqaatAndArazCity?.arazCity}
              miqaatId={miqaatAndArazCity?.miqaat}
              getKGList={() => getKGList(selectedFilters)}
              KGID={viewUserModal?.id}
              closeAddEditModal={closeViewUserModal}
            />
          }
          open={viewUserModal?.open ? true : false}
        />
      )}{' '}
      <LargeDataTable
        hideSearchField={hideTableBody}
        miqaatAndArazCity={miqaatAndArazCity}
        loading={loading || arazCityData?.loaing}
        searchValues={searchValues}
        filters={[
          ...KGFilters,
          {
            type: 'component',
            component: ({ allRows, headers }) => {
              return getUserDetail()?.role === SUPERADMIN ? (
                <Stack direction="row" width="100%" alignItems="center" justifyContent="flex-end">
                  <Button variant="contained" onClick={() => handleExportReport()} startIcon={<FileDownloadOutlinedIcon />}>
                    Export Excel
                  </Button>
                </Stack>
              ) : (
                <></>
              );
            },
            sm: 7.2,
            xs: 8,
            xl: 7.7
            // md: 7.7
          },
          {
            type: 'component',
            component: ({ allRows, headers }) => {
              return (
                <Stack direction="row" width="100%" alignItems="center" justifyContent="flex-end">
                  <Button
                    variant="contained"
                    onClick={openFilter}
                    sx={{ minWidth: '100px !important', width: '100%' }}
                    startIcon={<FilterListIcon />}
                  >
                    Filter
                  </Button>
                </Stack>
              );
            },
            xl: 1.3,
            // md: 1.3,
            xs: 4,
            sm: 1.8
          }
        ]}
        title="KG List"
        actionButtons={actionButtons}
        hideTableBody={hideTableBody}
        module="kgUser"
        hideMainCard={!miqaatAndArazCity?.miqaat || !miqaatAndArazCity?.arazCity || hideTableBody}
        columns={headers?.filter((header) => header?.name !== 'sync' || getUserDetail()?.role === SUPERADMIN)}
        tableContainerClassName="kg-list-table sticky-table-container"
        rows={kgList}
        tabFilter={KGUserStatusTab}
        isTabFilter={true}
        allRows={kgAllList || []}
        enablePagination={true}
        hideRemoveButton={true}
        removeStickyHeader
        isMiqaatArazCityFilter={true}
        isBackendPagination={true}
        totalCount={totalCount}
        pageNo={page}
        applyFilterOnKeyDown={true}
        onPageChange={(page) => onPageChange(page)}
        onPageLimitChange={(limit) => onPageLimitChange(limit)}
        onSelectChange={(data) => {
          if (!data?.miqaat || !data?.arazCity) {
            setSearchValues({ status: 'all' });
          }
          setMiqaatAndArazCity({ ...data });
        }}
        handleSort={handleSort}
        handleSearch={(filteredRows, searchValue, isSearch) => {
          setPage(0);
          setSearchValues({ ...searchValue });
          setMiqaatAndArazCity({ ...miqaatAndArazCity, ...searchValue });
        }}
      />
    </>
  );
};
export default KGUsers;
