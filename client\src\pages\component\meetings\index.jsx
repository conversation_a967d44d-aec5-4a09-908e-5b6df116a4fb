import React, { useEffect, useState } from 'react';
import { CommunicationMeetingPageUrl } from 'utils/constant';
import ActionButtons from '../table/tableComponent/actionButtons';
import { useNavigate } from 'react-router';
import { addPageUrl, checkIsUserSuperAdmin, editPageUrl, notification, showMiqaatValidationAlert, viewPageUrl } from 'utils/helper';
import { isMeetingInPast } from './constant';
import LargeDataTable from '../table';
import { get, isEmpty } from 'lodash';
import moment from 'moment';
import CustomModal from '../customModal';
import ConfirmationModal from '../customModal/confirmationModal';
import Mom from './mom';
import Actionable from './actionable';
import 'react-quill/dist/quill.snow.css';
import ActionableList from './actionableList';
import { useDispatch, useSelector } from 'react-redux';
import { addMinutesOfMeetingAction, deleteMeetingAction, getMeetingsListAction, sendNotificationAction } from 'redux/actions/meetingAction';
import { setMeetings } from 'redux/reducers/meetingReducer';
import SelectMiqaatAndArazCity from '../SelectMiqaatAndArazCity';
import Loader from 'components/Loader';
import { useAnalytics } from 'utils/userAnalytics';
import { convertUTCDateAndTimeToTimezone, convertUTCToTimezone } from 'utils/timeZoneHelper';

const Meetings = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const meetingData = useSelector((state) => state.meeting);
  const [meetingList, setMeetingList] = useState([]);
  const [allMeetingList, setAllMeetingList] = useState([]);
  const [deletedRowId, setDeletedRowId] = useState();
  const [momId, setMomId] = useState();
  const [isShowMOM, setIsShowMOM] = useState(false);
  const [kgUser, setKgUser] = useState();
  const [isShowActionable, setIsShowActionable] = useState(false);
  const [hideTableBody, setHideTableBody] = useState(true);
  const [miqaatAndArazCity, setMiqaatAndArazCity] = useState({});
  const [isShowActionableList, setIsShowActionableList] = useState(false);
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState('');
  const modalBtnStyle = { borderRadius: '10px' };
  const { trackUserActivity } = useAnalytics();

  const fetchMeetingList = async () => {
    setLoading(true);
    await dispatch(
      getMeetingsListAction({
        miqaatID: miqaatAndArazCity?.miqaat
        // arazCityID: miqaatAndArazCity?.arazCity
      })
    );
    setLoading(false);
    setHideTableBody(false);
  };

  useEffect(() => {
    if (miqaatAndArazCity && miqaatAndArazCity?.miqaat) {
      fetchMeetingList();
    } else {
      setHideTableBody(true);
      dispatch(setMeetings([]));
    }
  }, [miqaatAndArazCity?.arazCity, miqaatAndArazCity?.miqaat]);

  useEffect(() => {
    trackUserActivity({
      action: 'page_visit',
      page: 'Meetings',
      module: 'Communication'
    });
  }, []);

  useEffect(() => {
    const timeZoneConfig = JSON.parse(localStorage.getItem('selectedArazCityTimeZone'));
    const data = get(meetingData, 'meetings', []).map((item) => {
      const date = get(item, 'date');
      const time = get(item, 'time');

      let displayDateTime = '';
      if (date && time && timeZoneConfig) {
        displayDateTime = convertUTCDateAndTimeToTimezone(date, time, timeZoneConfig, 'MMMM DD, YYYY , hh:mm A');
      } else if (date && timeZoneConfig) {
        displayDateTime = convertUTCToTimezone(date, timeZoneConfig, 'MMMM DD, YYYY');
      }

      const isPast = isMeetingInPast(date, time);
      return {
        ...item,
        name: item.title,
        date: displayDateTime,
        meetingDone: isPast,
        isUpcomingMeeting: !isPast && !!date ? 'Upcoming meeting' : ''
      };
    });

    setMeetingList([...data]);
    setAllMeetingList([...data]);
  }, [meetingData?.meetings]);

  const handleSendReminder = async (meetingId) => {
    const response = await dispatch(
      sendNotificationAction({
        type: 'SEND_REMINDER',
        meetingID: meetingId
      })
    );
    if (get(response, 'payload.success')) {
      dispatch(notification(true, 'Reminder sent successfully!', false));
    }
  };

  const getBtnFunc = (name, row) => {
    if (name === 'actionable') {
      setIsShowActionable(true);
    }
    if (name === 'recordMinutes') {
      let { miqaat, arazCity } = miqaatAndArazCity || {};
      if (miqaat) {
        navigate(editPageUrl(CommunicationMeetingPageUrl, row._id));
      } else {
        showMiqaatValidationAlert(miqaatAndArazCity, dispatch);
      }
    }
    if (name === 'reminder') {
      handleSendReminder(row._id);
    }
  };

  const headers = [
    {
      name: 'name',
      type: 'text',
      title: 'Meeting Title',
      sortingactive: true,
      minWidth: '500px'
    },
    {
      name: 'date',
      type: 'text',
      title: 'Date',
      sortingactive: true,
      minWidth: '260px',
      includeTime: true
    },
    {
      name: 'isUpcomingMeeting',
      type: 'badge',
      title: 'Status',
      sortingactive: true,
      minWidth: '260px',
      includeTime: true
    },
    {
      name: 'actions',
      type: 'actions',
      title: 'Actions',
      sortingactive: false,
      width: '600px',
      component: ActionButtons,
      multipleButtons: [
        {
          type: 'meetingListBtns',
          action: 'view',
          variant: 'outlined',
          buttonOnClick: async (type, row, name) => {
            getBtnFunc(name, row);
          },
          color: 'primary'
        },
        {
          type: 'view',
          module: 'meeting',
          action: 'view',
          buttonOnClick: async (type, row) => {
            let { miqaat, arazCity } = miqaatAndArazCity || {};
            if (miqaat) {
              navigate(viewPageUrl(CommunicationMeetingPageUrl, row?._id));
            } else {
              showMiqaatValidationAlert(miqaatAndArazCity, dispatch);
            }
          },
          color: 'primary',
          tooltip: 'View'
        },
        {
          type: 'edit',
          module: 'meeting',
          action: 'edit',
          buttonOnClick: async (type, row) => {
            let { miqaat, arazCity } = miqaatAndArazCity || {};
            if (miqaat) {
              navigate(editPageUrl(CommunicationMeetingPageUrl, row._id));
            } else {
              showMiqaatValidationAlert(miqaatAndArazCity, dispatch);
            }
          },
          color: 'primary',
          tooltip: 'Edit'
        },
        {
          type: 'delete',
          module: 'meeting',
          action: 'delete',
          buttonOnClick: async (type, row) => {
            setDeletedRowId(row._id);
          },
          tooltip: 'Delete'
        }
      ]
    }
  ];

  const actionButtons = {
    title: 'Add Meeting',
    module: 'meeting',
    onClick: () => {
      let { miqaat, arazCity } = miqaatAndArazCity || {};
      if (miqaat) {
        navigate(`${addPageUrl(CommunicationMeetingPageUrl)}`);
      } else {
        showMiqaatValidationAlert(miqaatAndArazCity, dispatch);
      }
    }
  };

  const handleCloseDeleteModal = () => {
    setDeletedRowId();
  };

  const handleCloseMOMModal = () => {
    setIsShowMOM(false);
    setMomId();
  };

  const handleAddMom = () => {
    const requestedPayload = {
      id: momId,
      mom: value
    };
    dispatch(addMinutesOfMeetingAction(requestedPayload));
    handleCloseMOMModal();
  };

  const handleCloseActionableModal = () => {
    setIsShowActionable(false);
  };

  const handleCloseActionableListModal = () => {
    setIsShowActionableList(false);
  };

  const handleDeleteRow = async () => {
    const response = await dispatch(deleteMeetingAction({ id: deletedRowId }));
    if (get(response, 'payload.success')) {
      dispatch(notification(true, 'Meeting deleted successfully!'));
      handleCloseDeleteModal();
      fetchMeetingList();
    }
  };

  const deleteModalButtons = [
    { label: 'Cancel', onClick: () => handleCloseDeleteModal(), color: 'error' },
    { label: 'Confirm', onClick: () => handleDeleteRow(), variant: 'contained', color: 'primary' }
  ];

  const actionableModalButtons = [
    { label: 'Actionable List', onClick: () => setIsShowActionableList(true), variant: 'outlined', color: 'primary', style: modalBtnStyle },
    { label: 'Save', onClick: () => setKgUser(), variant: 'contained', color: 'primary', style: modalBtnStyle }
  ];

  const momModalButtons = [
    { label: 'Send Notification', onClick: () => handleCloseMOMModal(), variant: 'outlined', color: 'primary', style: modalBtnStyle },
    { label: 'Cancel', onClick: () => handleCloseMOMModal(), color: 'error', style: modalBtnStyle },
    { label: 'Submit', onClick: () => handleAddMom(), variant: 'contained', color: 'primary', style: modalBtnStyle }
  ];

  const resetPage = () => {
    dispatch(setMeetings([]));
  };

  return (
    <>
      {meetingData?.loading && <Loader />}
      <SelectMiqaatAndArazCity
        hideSearchField={true}
        values={miqaatAndArazCity}
        onSelectChange={(data) => {
          resetPage();
          setMiqaatAndArazCity({ ...data });
        }}
      />
      <LargeDataTable
        hideSearchField={false}
        loading={loading}
        // searchValues={searchValues}
        // filters={whiteListFilter}
        title="Meetings"
        // enableSearch
        actionButtons={actionButtons}
        module="meeting"
        columns={headers}
        tableContainerClassName="kg-list-table"
        rows={meetingList}
        allRows={allMeetingList || []}
        enablePagination={true}
        hideRemoveButton={true}
        miqaatAndArazCity={miqaatAndArazCity}
        onSelectChange={(data) => {
          resetPage();
          setMiqaatAndArazCity({ ...data });
        }}
        hideTableBody={!miqaatAndArazCity?.miqaat || hideTableBody}
        // isMiqaatArazCityFilter
        hideMainCard={hideTableBody}
        handleSearch={() => {}}
      />
      <CustomModal
        cancel={handleCloseDeleteModal}
        buttons={deleteModalButtons}
        borderRadius="20px"
        Component={<ConfirmationModal title="Meeting" />}
        open={deletedRowId}
      />
      <CustomModal
        cancel={handleCloseMOMModal}
        buttons={momModalButtons}
        borderRadius="20px"
        Component={<Mom value={value} setValue={setValue} momId={momId} />}
        open={isShowMOM}
        width="100%"
      />
      <CustomModal
        cancel={handleCloseActionableModal}
        buttons={actionableModalButtons}
        borderRadius="20px"
        Component={<Actionable kgUser={kgUser} setKgUser={setKgUser} handleCloseActionableModal={handleCloseActionableModal} />}
        open={isShowActionable}
        width="50%"
      />
      <CustomModal
        cancel={handleCloseActionableListModal}
        // buttons={actionableModalButtons}
        borderRadius="20px"
        Component={<ActionableList handleCloseActionableListModal={handleCloseActionableListModal} />}
        open={isShowActionableList}
        fullScreen
        width="80%"
      />
    </>
  );
};

export default Meetings;
