const express = require("express");
const router = express.Router();

const healthcheckRoute = require("./healthcheck.route");
const waazVenueRoute = require("./waazVenue.route");
const globalFunction = require("./globalFunction.route");
const zoneMapping = require("./zoneMapping.route");
const mawaidVenueRoute = require("./mawaidVenue.route");
const dashboard = require("./dashboard.route");
const arazCityZoneRoute = require("../../hierarchy/routes/arazCityZone.route"); 
const razaMappingRoute = require("./razaMapping.route")

router.use("/healthcheck", healthcheckRoute);
router.use("/waaz-venue", waazVenueRoute);
router.use("/mawaid-venue", mawaidVenueRoute);
router.use("/global-function", globalFunction);
router.use("/zone-mapping", zoneMapping);
router.use("/dashboard", dashboard);
router.use("/araz-city-zone", arazCityZoneRoute);
router.use("/raza-mapping", razaMappingRoute);

module.exports = router;
