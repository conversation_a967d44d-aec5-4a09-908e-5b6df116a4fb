import React from 'react';
import { TableCell, Button } from '@mui/material';
import { convertDateToStringFormat, formattedPrice } from 'utils/helper';
import { get } from 'lodash';
import { convertUTCToTimezone } from 'utils/timeZoneHelper';

const renderArrayCell = (items, name, type, dateFormat, includeTime, showToday, actionBtns, column, row) => {
  const timeZoneConfig = JSON.parse(localStorage.getItem('selectedArazCityTimeZone'));

  return (
    <TableCell>
      {items?.map((item, index) => {
        const cellData = get(item, name, '-');
        switch (type) {
          case 'date':
            return (
              <div key={index} style={{ marginTop: '10px' }}>
                {cellData
                  ? includeTime
                    ? convertUTCToTimezone(cellData, timeZoneConfig, 'MMMM DD, YYYY , hh:mm A', true)
                    : convertUTCToTimezone(cellData, timeZoneConfig, dateFormat || 'MMMM DD, YYYY')
                  : '-'}
              </div>
            );
          case 'price':
            return (
              <div key={index} style={{ marginTop: '10px' }}>
                {cellData ? formattedPrice(cellData) : '-'}
              </div>
            );
          case 'email':
            return (
              <div key={index} style={{ textTransform: 'lowercase', marginTop: '10px' }}>
                {cellData}
              </div>
            );
          case 'actions':
            return (
              <div key={index} style={{ marginTop: '10px' }}>
                {actionBtns?.map((actionBtn, btnIndex) => (
                  <Button
                    key={btnIndex}
                    size="small"
                    className="small-btn"
                    variant={actionBtn.variant || 'contained'}
                    color={item[actionBtn?.isDynamicBtnColor] || actionBtn.color || 'primary'}
                    onClick={() => actionBtn.onClick(row, item)}
                    disabled={item[actionBtn?.dynamicDisableBtn] || actionBtn.disabled}
                  >
                    {item[actionBtn?.title]}
                  </Button>
                ))}
              </div>
            );
          default:
            return (
              <div key={index} style={{ marginTop: '10px' }}>
                {cellData || '-'}
              </div>
            );
        }
      })}
    </TableCell>
  );
};

export default renderArrayCell;
