// Dependencies
const express = require("express");
const { createProxyMiddleware } = require("http-proxy-middleware");
const cors = require("cors");
const path = require("path");
const compression = require("compression");
const {
  GLOBAL_PORT,
  HIERARCHY_PORT,
  COMMUNICATION_PORT,
  TASK_MANAGEMENT_PORT,
  SURVEY_PORT,
  AUTH_SERVICE_PORT,
  CORS_ORIGIN,
  NODE_ENV,
} = require("./constants");
const webhookRoutes = require("./routes/webhook.route");
const publicRoutes = require("./public/public.route");
const { validateApiKey } = require("./middlewares/guard.middleware");

const swaggerUi = require("swagger-ui-express");
const { swaggerDocument } = require("./utils/swagger/swagger");
const { clearAllCache } = require("./utils/redis.cache");

// App
const app = express();
const PORT = process.env.GATEWAY_PORT;
app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
// app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));
app.use(express.static("public"));


app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res.status(400).json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});

app.use(
  compression({
    level: 6,
    filter: (req, res) => {
      if (req.headers["x-no-compression"]) {
        return false;
      }
      return compression.filter(req, res);
    },
  })
);

// Service map
const serviceMap = {
  global: GLOBAL_PORT,
  hierarchy: HIERARCHY_PORT,
  communication: COMMUNICATION_PORT,
  "task-management": TASK_MANAGEMENT_PORT,
  survey: SURVEY_PORT,
  auth: AUTH_SERVICE_PORT,
};

// Direct routes before proxy
app.use("/api/v1/docs", swaggerUi.serve, swaggerUi.setup(swaggerDocument, {
  explorer: true,
  customCss: ".swagger-ui .topbar { display: none }",
  swaggerOptions: {
    persistAuthorization: true,
  },
}));

app.use("/api/webhook", webhookRoutes);
app.use("/api/v1", validateApiKey, publicRoutes);



// Universal Proxy Route
app.use("/api/:serviceName/*", (req, res, next) => {
  const { serviceName } = req.params;
  const port = serviceMap[serviceName];

  if (!port) {
    return res.status(502).json({
      error: `Unknown service '${serviceName}'`,
      available: Object.keys(serviceMap),
    });
  }

  const target = `http://localhost:${port}`;
  const fullTargetPath = req.originalUrl;

  console.log(`➡️  Forwarding ${req.method} ${req.originalUrl} to ${target}${fullTargetPath}`);

  const proxy = createProxyMiddleware({
    target,
    changeOrigin: true,
    pathRewrite: () => fullTargetPath,
    logLevel: "debug",
    onError: (err, req, res) => {
      console.error(`❌ Proxy error:`, err.message);
      res.status(500).json({ error: "Proxy failed", detail: err.message });
    },
  });

  proxy(req, res, next);
});

// Production static serve
if (NODE_ENV === "production") {
  console.log("Production mode");
  app.use(express.static(path.join(__dirname, "client", "dist")));
  app.get("/", (req, res) => {
    res.sendFile(path.resolve(__dirname, "client", "dist", "index.html"));
  });
  app.get("/*", (req, res) => {
    res.sendFile(path.resolve(__dirname, "client", "dist", "index.html"));
  });
} else {
  app.get("/", (req, res) => {
    res.send(`AMS is running on port: ${PORT}`);
  });
}

const clearAllCacheFromRedis = async () => {
  console.log("clearing cache");
  await clearAllCache();
}
clearAllCacheFromRedis();

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 Gateway started on http://localhost:${PORT}`);
  Object.entries(serviceMap).forEach(([name, port]) =>
    console.log(`  • /api/${name}/* => http://localhost:${port}/api/*`)
  );
});

// Graceful shutdown
const gracefulShutdown = (signal) => {
  console.log(`\n📴 Received ${signal}. Shutting down gracefully...`);
  server.close((err) => {
    if (err) {
      console.error("❌ Error during server shutdown:", err);
      process.exit(1);
    }
    console.log("✅ Gateway server closed successfully");
    process.exit(0);
  });

  setTimeout(() => {
    console.error("⚠️  Forced shutdown after timeout");
    process.exit(1);
  }, 10000);
};

// Shutdown signals
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGHUP", () => gracefulShutdown("SIGHUP"));

