const express = require("express");
const cors = require("cors");
const path = require("path");
const compression = require("compression");
const { CORS_ORIGIN, NODE_ENV, TASK_MANAGEMENT_PORT } = require("../constants");
const routes = require("../modules/taskManagement/routes");
const { authGuard, roleGuard } = require("../middlewares/guard.middleware");
const { connectDB } = require("../db");
const { apiErrorLoggerMiddleware } = require("../middlewares/apiLogger.middleware");
const { addServiceHeader } = require("../middlewares/serviceHeader.middleware");

const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));

// Add service header middleware
app.use(addServiceHeader('task-management'));

app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});


// Task Management routes
app.use("/api", authGuard, roleGuard, routes);

app.get("/", (req, res) => {
  res.send(`Task Management Service is running on port: ${TASK_MANAGEMENT_PORT}`);
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    service: "task-management",
    port: TASK_MANAGEMENT_PORT,
    timestamp: new Date().toISOString()
  });
});

// Start server
// Add error logging middleware at the end
app.use(apiErrorLoggerMiddleware);
const startServer = async () => {
  try {
    await connectDB();
    app.listen(TASK_MANAGEMENT_PORT, () => {
      console.log(`Node environment: ${NODE_ENV}`);
      console.log(`Task Management Service running on port: ${TASK_MANAGEMENT_PORT}`);
    });
  } catch (err) {
    console.log(`DB Error: ${err.message}`);
    process.exit(1);
  }
};

if (require.main === module) {
  startServer();
}

module.exports = app;
