export const SUPERADMIN = 'super_admin';
export const ADMIN = 'pmo';
export const HODROLE = 'zone_head_default';
export const DEFAULTROLE = 'default';
export const MiqaatLocalStorageKey = 'selectedMiqaatAndCity';
export const MiqaatAndArazCityLocalStorageKey = 'superAdminMiqaatAndArazCity';
export const timeZoneConfig = JSON.parse(localStorage.getItem('selectedArazCityTimeZone'));

export const REMOVE_NON_DIGITS_REGEX = /[^0-9]/g;
export const REMOVE_NON_DIGITS_EXCEPT_NEWLINES_REGEX = /[^0-9\n]/g;
export const ID_8_DIGITS_REGEX = /^\d{8}$/;
export const EDITOR_TEXT_REGEX = /(<([^>]+)>)/gi;

export const ITS_ID_HB = '30351956';
export const AMS_SYSTEM = '67b6c8a233380165fa037613';
export const Drawing_tool_tip_Msg = 'After Approval, This file will be available for download';

export const ITS_Login_Url = 'https://www.its52.com/Login.aspx?OneLogin=ASHMNGMTST';

// ----------------------------------------------------API URL-------------------------------------------------------------------
// Hierarchy Api URL
export const HierarchyKGApiUrl = 'hierarchy/kg-user/';
export const loginApiUrl = `auth/user/its-one-login`;
export const getKGITSIDDetailApiUrl = (id) => `hierarchy/kg-user/add/get-its/${id}`;
export const GetPermissionsApiUrl = `auth/user/get/encrypted-permissions`;
export const addOneSignalDeviceApiUrl = `/hierarchy/kg-user/add/one-signal-device`;
export const UpdateConsentStatusApiUrl = `/hierarchy/kg-user/edit/update-consent-status`;

//Global Master

export const HierarchyMiqaatApiUrl = 'global/global-master/miqaat/';
export const HierarchyArazCityApiUrl = 'global/global-master/araz-city/';
export const HierarchyDepartmentApiUrl = 'global/global-master/department/';
export const HierarchyFunctionApiUrl = 'global/global-master/function/';
export const HierarchyKGTypeApiUrl = 'global/global-master/kg-type/';
export const HierarchyKGGroupApiUrl = 'global/global-master/kg-group/';
export const HierarchyPositionApiUrl = 'global/global-master/kg-hierarchy-position/';
export const HierarchySMEMappingApiUrl = 'global/global-master/sme-mapping/';
export const HierarchyDeptQuotaApiUrl = 'global/global-master/department-quota/';
export const HierarchyKgRequisitionApiUrl = 'hierarchy/kg-requisition/';
export const HierarchyKgRequisitionByDeptApiUrl = 'hierarchy/kg-requisition-by-department/';
export const HierarchyKgRequisitionByZoneApiUrl = 'hierarchy/kg-requisition-by-zone';

export const AsharaGuideApiUrl = 'global/global-master/guide/';

export const DocumentManagerApiUrl = 'global/document-manager/document/';
export const DocumentManagerDefaultViewApiUrl = 'global/document-manager/document-default-view/';

export const ZoneCapacityWaazVenueTypeApiUrl = 'global/global-master/waaz-venue-type/';
export const ZoneCapacityWaazSeatingCapacityApiUrl = 'global/global-master/waaz-seating-capacity/';
export const ZoneCapacityWaazVenuePriorityApiUrl = 'global/global-master/waaz-venue-priority/';
export const ZoneCapacityWaazVenueSuitabilityApiUrl = 'global/global-master/waaz-venue-suitability/';
export const ZoneCapacityMawaidVenueTypeApiUrl = 'global/global-master/mawaid-venue-type/';
export const ZoneCapacityMawaidVenueSuitabilityApiUrl = 'global/global-master/mawaid-venue-suitability/';

//Hierarchy
export const HierarchyDepReportApiUrl = 'hierarchy/department-report/';
export const HierarchyActiveDeactiveDepApiUrl = 'hierarchy/araz-city-department/';
export const GetAllJamiatJamaatApiUrl = 'hierarchy/jamiat-jamaat/get';
export const SystemUserApiUrl = 'hierarchy/system-user/';
export const HierarchySystemRoleApiUrl = 'hierarchy/system-role/';
export const HierarchyApiUrl = 'hierarchy/hierarchy/';
export const HierarchyDashboardApiUrl = 'hierarchy/dashboard';
export const HierarchyPrintIDCardApiUrl = 'hierarchy/print/';
export const HierarchyUnPrintIDCardApiUrl = 'hierarchy/unprint/';
export const HierarchyZoneReportApiUrl = 'hierarchy/araz-city-zone-report/get';
export const HierarchyZoneApiUrl = 'hierarchy/araz-city-zone';

// Communication
export const CommunicationDashboardApiUrl = 'communication/dashboard/';
export const CommunicationInboxApiUrl = 'communication/inbox/';
export const CommunicationComposeApiUrl = 'communication/compose/';
export const CommunicationScheduleApiUrl = 'communication/schedule/';
export const CommunicationEmailReportApiUrl = 'communication/email-report/';
export const CommunicationEmailLogApiUrl = 'communication/email-logs/';
export const CommunicationMeetingApiUrl = 'communication/meeting/';

// Ashara Guide
export const AsharaGuideDashboardApiUrl = 'global/ashara-guide/dashboard/';
export const AsharaGuideDefaultViewApiUrl = 'global/ashara-guide/guide-default-view/';

// Zone And Capacity

export const ZoneCapacityHotelsApiUrl = 'zones-capacity/hotels/';
export const HotelsApiUrl = 'accomodation/hotel/';
export const ZoneCapacityDashboardApiUrl = 'zones-capacity/dashboard/';
export const ZoneCapacityWaazVenueApiUrl = 'zones-capacity/waaz-venue/';
export const ZoneCapacityMawaidVenueApiUrl = 'zones-capacity/mawaid-venue/';
export const ZoneCapacityZoneFileApiUrl = 'zones-capacity/araz-city-zone';
export const ZoneCapacityZoneMappingApiUrl = 'zones-capacity/zone-mapping/';
export const ZoneCapacityZoneApiUrl = 'zones-capacity/araz-city-zone/';

// Task Management
export const TaskManagementApiUrl = 'task-management/';

// HR
export const HRKhidmatRequestApiUrl = 'hierarchy/interest/';

//Accomodation
export const AccomodationArazCityReportApiUrl = 'global/accomodation/araz-city-report/get';

//notification
export const NotificationApiUrl = 'global/global-master/notification/';

//dynamic form
export const QuestionnaireFormApiUrl = 'survey/question/';
export const AllQuestionFormApiUrl = 'survey/all-question/';
export const SurveyFormApiUrl = 'survey/survey-forms/';
export const AllSurveyFormApiUrl = 'survey/all-survey-forms/';
export const ResponseSurveyFormApiUrl = 'survey/survey-response/';

// Report
export const WhiteListOneDashboardAPIUrl = 'global/report/white-list-dashboard-1/get';
export const WhiteListTwoDashboardAPIUrl = 'global/report/white-list-dashboard-2';
export const ArazCityHRDashboardAction = 'global/report/araz-city-hr-dashboard';
export const ArazCityReportAction = 'global/report/araz-city-dashboard';
export const ArazCityArrivalReportAction = 'global/report/arrival-report';

// ----------------------------------------------------Route URL-------------------------------------------------------------------

export const ModuleSelectionPageUrl = '/module-selection';
export const MiqaatSelectionPageUrl = '/miqaat-selection';
export const ConsentPageUrl = '/consent';
export const loginPageUrl = '/login';
export const ITSLoginVerificationPageUrl = '/its_one_login';
export const AccessDeniedPageUrl = '/access-denied';
export const UserProfilePageUrl = '/profile';
export const pageNotFoundPageUrl = '/404';

//Global Master

export const HierarchyMiqaatPageUrl = '/global-master/miqaats';
export const HierarchyArazCityPageUrl = '/global-master/araz-city';
export const HierarchyArazCityZonePageUrl = '/global-master/araz-city-zone';
export const HierarchyDepartmentsPageUrl = '/hierarchy/departments';
export const GlobalMasterDepartmentsPageUrl = '/global-master/departments';
export const HierarchyFunctionPageUrl = '/global-master/functions';
export const HierarchyKGGroupPageUrl = '/global-master/kg-group';
export const HierarchyKGtypePageUrl = '/global-master/kg-type';
export const HierarchyDepartmentQuotaPageUrl = '/global-master/departmental-quota';
export const HierarchyDeptSMEMappingPageUrl = '/global-master/dept-sme-mapping';
export const HierarchyPositionPageUrl = '/global-master/hierarchy-position';

//Hierarchy
export const HierarchyKGRequisitionPageUrl = '/hierarchy/kg-requisition';
export const HierarchyDashboardPageUrl = '/hierarchy/dashboard';
export const HierarchyArazCityReportPageUrl = '/hierarchy/araz-city-report';
export const HierarchySystemUsersPageUrl = '/hierarchy/system-users';
export const HierarchySystemRolesPageUrl = '/hierarchy/system-role';
export const HierarchyKGListPageUrl = '/hierarchy/kg-list';
export const HierarchyPrintIDCardPageUrl = '/hierarchy/print-id-card';
export const HierarchyUnPrintIDCardPageUrl = '/hierarchy/unprint-id-card';
export const HierarchyWhiteListCompilePageUrl = '/hierarchy/compile-white-list';
export const HierarchyWhiteListRecommendationsPageUrl = '/hierarchy/white-list-recommendations';
export const HierarchyApprovedForRazaWhiteListPageUrl = '/hierarchy/approved-for-raza-white-list';
export const HierarchyAssignHierarchyPositionImportFilePageUrl = '/hierarchy/hierarchy-position-import';
export const HierarchyAssignHierarchyPositionByITSPageUrl = '/hierarchy/hierarchy-position-by-itsID';
export const HierarchyPageUrl = '/hierarchy';
export const HierarchyTreeViewPageUrl = '/hierarchy/tree-view';
export const HierarchyDepReportPageUrl = '/hierarchy/report/department';
export const HierarchyDepHODReportPageUrl = '/hierarchy/report/department-hod';
export const HierarchyZonalReportPageUrl = '/hierarchy/report/zone';
export const HierarchyZonalHODReportPageUrl = '/hierarchy/report/zone-hod';
export const HierarchyKGRequestReportPageUrl = '/hierarchy/report/kg-request';
export const HierarchyKGRequisitionReportPageUrl = '/hierarchy/report/kg-requisition';
export const HierarchyZonalTeamReportPageUrl = '/hierarchy/report/zonal-department-team';
export const HierarchyByDepartmentPageUrl = '/hierarchy/by-department';
export const HierarchyByZonePageUrl = '/hierarchy/by-zone';

// Communication

export const CommunicationDashboardPageUrl = '/communication/dashboard';
export const CommunicationInboxPageUrl = '/communication/inbox';
export const CommunicationSentPageUrl = '/communication/sent';
export const CommunicationSchedulePageUrl = '/communication/schedule';
export const CommunicationComposePageUrl = '/communication/compose';
export const CommunicationEmailReportePageUrl = '/communication/email-report';
export const CommunicationEmailLogsPageUrl = '/communication/email-logs';
export const CommunicationMeetingPageUrl = '/communication/meeting';
export const CommunicationMeetingLogsPageUrl = '/communication/meeting-logs';

// Ashara Guide

export const AsharaGuideDashboardPageUrl = '/ashara-guide/dashboard';
export const AsharaGuidePageUrl = '/ashara-guide';
export const AsharaGuideReportPageUrl = '/ashara-guide/report';

// Document Manager

export const DocumentManagerDashboardPageUrl = '/document-manager/dashboard';
export const DocumentManagerPageUrl = '/document-manager';
export const MyDocumentManagerPageUrl = '/my-document-manager';
export const DocumentMAnagerReportPageUrl = '/document-manager/report';

// Zone And Capacity

export const ZoneCapacityDashboarPageUrl = '/zone-capacity/dashboard';
export const ZoneCapacityWaazVenuePageUrl = '/zone-capacity/waaz-venue';
export const ZoneCapacityMawaidVenuePageUrl = '/zone-capacity/mawaid-venue';
export const ZoneCapacityZonesPageUrl = '/zone-capacity/zones';
export const ZoneCapacityWaazVenueTypePageUrl = '/zone-capacity/waaz-venue-type';
export const ZoneCapacityWaazSeatingCapacityPageUrl = '/zone-capacity/waaz-seating-capacity';
export const ZoneCapacityWaazVenueSuitabilityPageUrl = '/zone-capacity/waaz-venue-suitability';
export const ZoneCapacityWaazVenuePriorityPageUrl = '/zone-capacity/waaz-venue-priority';
export const ZoneCapacityMawaidVenueTypePageUrl = '/zone-capacity/mawaid-venue-type';
export const ZoneCapacityMawaidVenueSuitabilityPageUrl = '/zone-capacity/mawaid-venue-suitability';
export const ZoneCapacityHotelsPageUrl = '/zone-capacity/hotels';
export const ZoneCapacityZoneMappingPageUrl = '/zone-capacity/zone-mapping';
export const hofDataUrl = 'zones-capacity/zone-mapping/get/hof-data';

// Task Management
export const TaskManagementPageUrl = '/task-management';

// Task Management
export const AccomodationHotelPageUrl = '/accomodation/hotel';

// HR
export const HRKhidmatRequestPageUrl = '/hr/khidmat-request';
export const RegistKGPageUrl = '/hierarchy/add-Kg';
export const BulkDeleteKGPageUrl = '/hierarchy/bulk-delete-Kg';

// Report
export const ArazCityReportPageUrl = '/report/araz-city';
export const ArrivalInfoPageUrl = '/report/araz-city/arrival-info';
export const ArazCityZoneReportPageUrl = '/report/araz-city-zone';
export const ZoneWiseReportPageUrl = '/report/zone-wise';
export const DepartmentWiseReportPageUrl = '/report/department-wise';
export const ArazCityHRDashboardReportPageUrl = '/report/araz-city-hr-dashboard';
export const WhiteListOneDashboardReportPageUrl = '/report/white-list-one-dashboard';
export const WhiteListTwoDashboardReportPageUrl = '/report/white-list-two-dashboard';
export const AnalyticsReportUrl = '/report/analytics-report';
// Feroz Bhai
export const FerozBhaiDashboardPageUrl = '/dashboard';
export const FerozBhaiAddUserPageUrl = '/dashboard/add-user';
export const FerozBhaiAddPermissionPageUrl = '/dashboard/add-permission';

export const showAllNotificationPageUrl = '/show-all-notification';

//Dynamic Form
export const QuestionnaireFormPageUrl = '/survey/questionnaire';
export const ViewAllQuestionsPageUrl = '/survey/all-questions';
export const SurveyPageUrl = '/survey/survey';
export const ViewAllSurveyPageUrl = '/survey/all-survey';
export const ViewSurveyReportPageUrl = '/survey/report';
export const AttendSurveyPageUrl = '/survey/attend-survey';
export const ViewReportPageUrl = '/survey/view-report';
export const ViewAllReportPageUrl = '/survey/view-all-report';
export const ViewSurveyResponsePageUrl = '/survey/view-response';
